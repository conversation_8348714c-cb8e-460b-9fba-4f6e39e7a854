2025-07-02 16:36:37 - plugin.example_plugin - ERROR - 加载示例插件页面失败: example_plugin/index.html
2025-07-02 17:45:05 - plugin_manager - ERROR - 加载插件失败: pcapChange, 错误: BasePlugin.__init__() takes 1 positional argument but 2 were given
2025-07-02 17:45:05 - core.app - ERROR - 插件加载失败: pcapChange
2025-07-02 17:45:05 - plugin_manager - ERROR - 加载插件失败: plugin_template, 错误: BasePlugin.__init__() takes 1 positional argument but 2 were given
2025-07-02 17:45:05 - core.app - ERROR - 插件加载失败: plugin_template
2025-07-02 17:45:05 - plugin_manager - ERROR - 加载插件失败: example_plugin, 错误: BasePlugin.__init__() takes 1 positional argument but 2 were given
2025-07-02 17:45:05 - core.app - ERROR - 插件加载失败: example_plugin
2025-07-02 18:05:14 - plugin_manager - ERROR - 加载插件 plugin_template 失败: Can't instantiate abstract class Plugin with abstract methods get_description, get_display_name, get_version
2025-07-02 18:05:14 - core.app - ERROR - 插件加载失败: plugin_template
2025-07-02 18:05:14 - plugin_manager - ERROR - 加载插件 example_plugin 失败: Can't instantiate abstract class Plugin with abstract methods get_description, get_display_name, get_version
2025-07-02 18:05:14 - core.app - ERROR - 插件加载失败: example_plugin
2025-07-02 18:07:14 - plugin_manager - ERROR - 加载插件 plugin_template 失败: core.base_plugin.BasePlugin.__init__() got multiple values for keyword argument 'name'
2025-07-02 18:07:14 - core.app - ERROR - 插件加载失败: plugin_template
2025-07-02 18:07:14 - plugin_manager - ERROR - 加载插件 example_plugin 失败: core.base_plugin.BasePlugin.__init__() got multiple values for keyword argument 'name'
2025-07-02 18:07:14 - core.app - ERROR - 插件加载失败: example_plugin
2025-07-02 18:07:15 - plugin_manager - ERROR - 加载插件 plugin_template 失败: core.base_plugin.BasePlugin.__init__() got multiple values for keyword argument 'name'
2025-07-02 18:07:15 - core.app - ERROR - 插件加载失败: plugin_template
2025-07-02 18:07:15 - plugin_manager - ERROR - 加载插件 example_plugin 失败: core.base_plugin.BasePlugin.__init__() got multiple values for keyword argument 'name'
2025-07-02 18:07:15 - core.app - ERROR - 插件加载失败: example_plugin
2025-07-02 18:07:50 - plugin_manager - ERROR - 加载插件 plugin_template 失败: core.base_plugin.BasePlugin.__init__() got multiple values for keyword argument 'name'
2025-07-02 18:07:50 - core.app - ERROR - 插件加载失败: plugin_template
2025-07-02 18:07:50 - plugin_manager - ERROR - 加载插件 example_plugin 失败: 'Plugin' object has no attribute 'name'
2025-07-02 18:07:50 - core.app - ERROR - 插件加载失败: example_plugin
2025-07-02 18:08:06 - plugin_manager - ERROR - 加载插件 plugin_template 失败: 'Plugin' object has no attribute 'name'
2025-07-02 18:08:06 - core.app - ERROR - 插件加载失败: plugin_template
2025-07-02 18:08:06 - plugin_manager - ERROR - 加载插件 example_plugin 失败: 'Plugin' object has no attribute 'name'
2025-07-02 18:08:06 - core.app - ERROR - 插件加载失败: example_plugin
2025-07-02 18:10:23 - plugin_manager - ERROR - 加载插件 plugin_template 失败: 'Plugin' object has no attribute 'name'
2025-07-02 18:10:23 - core.app - ERROR - 插件加载失败: plugin_template
2025-07-02 18:10:23 - plugin_manager - ERROR - 加载插件 example_plugin 失败: 'Plugin' object has no attribute 'name'
2025-07-02 18:10:23 - core.app - ERROR - 插件加载失败: example_plugin
2025-07-02 19:56:49 - plugin.unknown - ERROR - 加载示例插件页面失败: 'plugin_info' is undefined
2025-07-03 11:47:01 - plugin_manager - ERROR - 加载插件 pcapChange 失败: cannot import name 'PATH' from 'run' (/home/<USER>/pythonproject/persion/tools/integrated_platform/run.py)
2025-07-03 11:47:01 - core.app - ERROR - 插件加载失败: pcapChange
2025-07-03 11:47:02 - plugin_manager - ERROR - 加载插件 pcapChange 失败: cannot import name 'PATH' from 'run' (/home/<USER>/pythonproject/persion/tools/integrated_platform/run.py)
2025-07-03 11:47:02 - core.app - ERROR - 插件加载失败: pcapChange
2025-07-03 13:43:20 - plugin.unknown - ERROR - 插件初始化失败: 报文修改工具, 错误: 'Plugin' object has no attribute 'testPage'
2025-07-03 14:06:06 - plugin.unknown - ERROR - tcpprep failed with exit code 255, result:  Fatal Error: Error opening file: unknown file format
2025-07-03 14:06:06 - plugin.unknown - ERROR - tcpprep failed,can not get cache
2025-07-03 14:06:06 - plugin.unknown - ERROR - 处理文件失败: test.pcap
2025-07-03 14:06:06 - api_manager - ERROR - 插件API执行失败: pcapChange, 错误: Object of type Response is not JSON serializable
2025-07-04 02:52:20 - plugin.unknown - ERROR - 加载报文回放插件页面失败: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-07-04 02:52:26 - plugin.unknown - ERROR - 加载报文回放插件页面失败: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-07-04 03:31:22 - plugin.unknown - ERROR - 加载报文回放插件页面失败: Encountered unknown tag 'endblock'.
2025-07-04 03:31:23 - plugin.unknown - ERROR - 加载报文回放插件页面失败: Encountered unknown tag 'endblock'.
2025-07-04 03:31:37 - plugin.unknown - ERROR - 加载报文回放插件页面失败: Encountered unknown tag 'endblock'.
2025-07-04 06:12:18 - plugin.unknown - ERROR - 创建任务失败: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
2025-07-04 06:12:18 - api_manager - ERROR - 插件API执行失败: pcapPlay, 错误: Object of type Response is not JSON serializable
2025-07-04 07:05:46 - api_manager - ERROR - 插件API执行失败: pcapPlay, 错误: Plugin.deleteTask() got an unexpected keyword argument 'task_id'
2025-07-04 08:00:02 - core.app - ERROR - 应用关闭失败: 'PluginManager' object has no attribute 'get_loaded_plugin_names'
2025-07-06 11:20:39 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'startTask'
2025-07-06 12:10:26 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 12:12:38 - plugin_manager - ERROR - 加载插件 pcapPlay 失败: No module named 'pcapPlay.pcapreplay'
2025-07-06 12:12:38 - core.app - ERROR - 插件加载失败: pcapPlay
2025-07-06 12:12:53 - plugin_manager - ERROR - 加载插件 pcapPlay 失败: No module named 'pcapreplay'
2025-07-06 12:12:53 - core.app - ERROR - 插件加载失败: pcapPlay
2025-07-06 12:41:28 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 12:59:43 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 13:14:30 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 13:21:26 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 13:22:01 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 13:22:26 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 13:38:35 - plugin_manager - ERROR - 加载插件 pcapPlay 失败: unindent does not match any outer indentation level (plugin.py, line 568)
2025-07-06 13:38:35 - core.app - ERROR - 插件加载失败: pcapPlay
2025-07-06 13:41:30 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 13:50:08 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 13:50:09 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'getTask'
2025-07-06 13:51:33 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'startTask'
2025-07-06 13:52:57 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'startTask'
2025-07-07 06:25:38 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'startTask'
2025-07-07 06:25:39 - plugin.unknown - ERROR - 插件初始化失败: 报文回放工具, 错误: 'Plugin' object has no attribute 'startTask'
2025-07-07 06:40:56 - plugin.unknown - ERROR - Error reading device config: 'Response' object is not subscriptable
2025-07-07 06:40:56 - plugin.unknown - ERROR - Error in preTest: 'Response' object is not subscriptable
2025-07-07 06:40:56 - plugin.unknown - ERROR - PreTest failed: 'Response' object is not subscriptable
2025-07-07 06:40:56 - plugin.unknown - ERROR - 任务 test1 (ID: 199237f3-04eb-4e35-be29-1006589366e0) 执行失败: 'Response' object is not subscriptable
2025-07-07 06:40:56 - plugin.unknown - ERROR - 创建任务失败: 'Response' object is not subscriptable
2025-07-07 06:40:56 - api_manager - ERROR - 插件API执行失败: pcapPlay, 错误: Object of type Response is not JSON serializable
2025-07-07 06:42:40 - plugin.unknown - ERROR - Error reading device config: 'Response' object is not subscriptable
2025-07-07 06:42:40 - plugin.unknown - ERROR - Error in preTest: 'Response' object is not subscriptable
2025-07-07 06:42:40 - plugin.unknown - ERROR - PreTest failed: 'Response' object is not subscriptable
2025-07-07 06:42:40 - plugin.unknown - ERROR - 任务 test1 (ID: 89db7044-792d-4c1b-8714-9ba54e60b7df) 执行失败: 'Response' object is not subscriptable
2025-07-07 06:42:40 - plugin.unknown - ERROR - 创建任务失败: 'Response' object is not subscriptable
2025-07-07 06:42:40 - api_manager - ERROR - 插件API执行失败: pcapPlay, 错误: Object of type Response is not JSON serializable
2025-07-07 06:43:46 - plugin.unknown - ERROR - Error reading device config: 'Response' object is not subscriptable
2025-07-07 06:43:46 - plugin.unknown - ERROR - Error in preTest: 'Response' object is not subscriptable
2025-07-07 06:43:46 - plugin.unknown - ERROR - PreTest failed: 'Response' object is not subscriptable
2025-07-07 06:43:46 - plugin.unknown - ERROR - 任务 test1 (ID: 3b41c348-6bae-4b68-8f34-8e87c4562401) 执行失败: 'Response' object is not subscriptable
2025-07-07 06:43:46 - plugin.unknown - ERROR - 创建任务失败: 'Response' object is not subscriptable
2025-07-07 06:43:46 - api_manager - ERROR - 插件API执行失败: pcapPlay, 错误: Object of type Response is not JSON serializable
2025-07-07 06:48:55 - plugin.unknown - ERROR - Error reading device config: 'Response' object is not subscriptable
2025-07-07 06:48:55 - plugin.unknown - ERROR - Error in preTest: 'Response' object is not subscriptable
2025-07-07 06:48:55 - plugin.unknown - ERROR - PreTest failed: 'Response' object is not subscriptable
2025-07-07 06:48:55 - plugin.unknown - ERROR - 任务 test1 (ID: 34ee265e-1185-4cad-9635-82dd718c5e05) 执行失败: 'Response' object is not subscriptable
2025-07-07 06:48:55 - plugin.unknown - ERROR - 创建任务失败: 'Response' object is not subscriptable
2025-07-07 06:48:55 - api_manager - ERROR - 插件API执行失败: pcapPlay, 错误: Object of type Response is not JSON serializable
2025-07-21 17:39:49 - plugin.unknown - ERROR - 插件初始化失败: 网络模拟访问, 错误: 'Plugin' object has no attribute 'batchDeleteTasks'
2025-07-21 19:25:29 - plugin.unknown - ERROR - 网络模拟访问失败: 'NetworkSimulator' object has no attribute '_generate_ip_address'
2025-07-21 19:26:58 - plugin.unknown - ERROR - Error in pcapReplay.rewrite: name 'PATH' is not defined
2025-07-21 19:26:58 - plugin.unknown - ERROR - 网络模拟访问失败: stat: path should be string, bytes, os.PathLike or integer, not NoneType
2025-07-21 19:33:18 - plugin.unknown - ERROR - tcpprep failed with exit code 255, result:  Fatal Error: No packets were processed.  Filter too limiting?
2025-07-21 19:33:18 - plugin.unknown - ERROR - Failed to cleanup old pcap file None: rename: src should be string, bytes or os.PathLike, not NoneType
2025-07-21 19:33:21 - plugin.unknown - ERROR - tcpprep failed with exit code 255, result:  Fatal Error: No packets were processed.  Filter too limiting?
2025-07-21 19:33:21 - plugin.unknown - ERROR - Failed to cleanup old pcap file None: rename: src should be string, bytes or os.PathLike, not NoneType
2025-07-21 19:35:33 - plugin.unknown - ERROR - tcpprep failed with exit code 255, result:  Fatal Error: No packets were processed.  Filter too limiting?
2025-07-21 19:35:33 - plugin.unknown - ERROR - Failed to cleanup old pcap file None: rename: src should be string, bytes or os.PathLike, not NoneType
2025-07-21 19:38:45 - plugin.unknown - ERROR - tcpprep failed with exit code 255, result:  Fatal Error: No packets were processed.  Filter too limiting?
2025-07-21 19:38:45 - plugin.unknown - ERROR - Failed to cleanup old pcap file None: rename: src should be string, bytes or os.PathLike, not NoneType
2025-07-21 19:40:42 - plugin.unknown - ERROR - tcpprep failed with exit code 255, result:  Fatal Error: No packets were processed.  Filter too limiting?
2025-07-21 19:40:42 - plugin.unknown - ERROR - Failed to cleanup old pcap file None: rename: src should be string, bytes or os.PathLike, not NoneType
2025-07-21 19:42:29 - plugin.unknown - ERROR - tcpprep failed with exit code 255, result:  Fatal Error: No packets were processed.  Filter too limiting?
2025-07-21 21:28:31 - plugin.unknown - ERROR - 加载创建任务页面失败: Could not build url for endpoint 'plugins.httpLoadSimulator.static' with values ['filename']. Did you mean 'plugin_httpLoadSimulator_index' instead?
2025-07-22 09:48:42 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 09:48:42 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:26:07 - plugin.unknown - ERROR - 创建任务报文压缩包失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/http_load_report_78a9c0bc-300e-4155-888e-66ebf6b57744.zip'
2025-07-22 10:54:45 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:54:45 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:54:45 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:54:49 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:54:49 - plugin.unknown - ERROR - 子任务 0917185d-a881-4746-a00e-a619010ee2ef_1 执行失败: 抓包文件生成失败或为空
2025-07-22 10:54:49 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:54:49 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:54:53 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:54:53 - plugin.unknown - ERROR - 子任务 0917185d-a881-4746-a00e-a619010ee2ef_2 执行失败: 抓包文件生成失败或为空
2025-07-22 10:54:53 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 10:54:53 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:03:28 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:03:28 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:03:32 - plugin.unknown - ERROR - 测试项目 '登录接口测试' 执行失败: 抓包文件生成失败或为空
2025-07-22 11:03:32 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:03:36 - plugin.unknown - ERROR - 测试项目 '用户信息查询' 执行失败: 抓包文件生成失败或为空
2025-07-22 11:03:36 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:03:40 - plugin.unknown - ERROR - 测试项目 '数据提交测试' 执行失败: 抓包文件生成失败或为空
2025-07-22 11:03:40 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:03:40 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:19:45 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:19:45 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:19:45 - plugin.unknown - ERROR - HTTP负载解析失败
2025-07-22 11:19:45 - plugin.unknown - ERROR - 测试项目 '测试项目' 执行失败: HTTP负载解析失败，请检查格式是否正确
2025-07-22 11:19:45 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:19:49 - plugin.unknown - ERROR - 测试项目 '登录接口测试' 执行失败: 抓包文件生成失败或为空
2025-07-22 11:19:49 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:19:53 - plugin.unknown - ERROR - 测试项目 '用户信息查询' 执行失败: 抓包文件生成失败或为空
2025-07-22 11:19:53 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:19:57 - plugin.unknown - ERROR - 测试项目 '数据提交测试' 执行失败: 抓包文件生成失败或为空
2025-07-22 11:19:57 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:19:57 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 11:45:04 - plugin.unknown - ERROR - HTTP负载解析失败
2025-07-22 11:45:04 - plugin.unknown - ERROR - 测试项目 '测试项目' 执行失败: HTTP负载解析失败，请检查格式是否正确
2025-07-22 12:04:01 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:04:01 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:04:01 - plugin.unknown - ERROR - HTTP负载解析失败
2025-07-22 12:04:01 - plugin.unknown - ERROR - 测试项目 '测试项目' 执行失败: HTTP负载解析失败，请检查格式是否正确
2025-07-22 12:04:01 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:04:05 - plugin.unknown - ERROR - 测试项目 '简单GET请求' 执行失败: 抓包文件生成失败或为空
2025-07-22 12:04:05 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:04:09 - plugin.unknown - ERROR - 测试项目 '简单POST请求' 执行失败: 抓包文件生成失败或为空
2025-07-22 12:04:09 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:04:09 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:23:04 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:23:04 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:23:04 - plugin.unknown - ERROR - HTTP负载解析失败
2025-07-22 12:23:04 - plugin.unknown - ERROR - 测试项目 '测试项目' 执行失败: HTTP负载解析失败，请检查格式是否正确
2025-07-22 12:23:04 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:23:08 - plugin.unknown - ERROR - 测试项目 'API健康检查' 执行失败: 抓包文件生成失败或为空
2025-07-22 12:23:08 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:23:12 - plugin.unknown - ERROR - 测试项目 '用户登录接口' 执行失败: 抓包文件生成失败或为空
2025-07-22 12:23:12 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 12:23:12 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 13:56:40 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 13:59:49 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 13:59:49 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 13:59:49 - plugin.unknown - ERROR - HTTP负载解析失败
2025-07-22 13:59:49 - plugin.unknown - ERROR - 测试项目 '测试项目' 执行失败: HTTP负载解析失败，请检查格式是否正确
2025-07-22 13:59:49 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 13:59:53 - plugin.unknown - ERROR - 测试项目 '修复测试1' 执行失败: 抓包文件生成失败或为空
2025-07-22 13:59:53 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 13:59:57 - plugin.unknown - ERROR - 测试项目 '修复测试2' 执行失败: 抓包文件生成失败或为空
2025-07-22 13:59:57 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 13:59:57 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:01:51 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 14:06:28 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:06:28 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:06:28 - plugin.unknown - ERROR - HTTP负载解析失败
2025-07-22 14:06:28 - plugin.unknown - ERROR - 测试项目 '测试项目' 执行失败: HTTP负载解析失败，请检查格式是否正确
2025-07-22 14:06:28 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:06:32 - plugin.unknown - ERROR - 测试项目 '调试测试' 执行失败: 抓包文件生成失败或为空
2025-07-22 14:06:32 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:06:32 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:11:29 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 14:14:38 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 14:15:47 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 14:16:18 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 14:17:15 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 14:20:47 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:20:47 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:21:01 - plugin.unknown - ERROR - 测试项目 '数据格式测试' 执行失败: 抓包文件生成失败或为空
2025-07-22 14:21:01 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:21:01 - plugin.unknown - ERROR - 保存任务数据失败: [Errno 13] Permission denied: '/home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/tasks.json'
2025-07-22 14:25:40 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 14:26:38 - plugin.unknown - ERROR - 创建批量任务失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-07-22 14:29:32 - plugin.unknown - ERROR - 解析CSV文件失败: 'utf-8' codec can't decode byte 0xcf in position 0: invalid continuation byte
2025-07-22 14:29:47 - plugin.unknown - ERROR - 解析CSV文件失败: 'utf-8' codec can't decode byte 0xcf in position 0: invalid continuation byte
