#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心应用
"""

import os
import logging
from flask import Flask, render_template, jsonify, send_from_directory, abort
from flask_sqlalchemy import SQLAlchemy

from .plugin_manager import PluginManager
from .route_registry import RouteRegistry
from .api_manager import APIManager
from .log_manager import LogManager


# 全局数据库实例
db = SQLAlchemy()


def create_app(config_name: str = 'development') -> Flask:
    """创建Flask应用实例"""

    # 创建Flask应用
    project_root = os.path.dirname(os.path.dirname(__file__))
    template_folders = [
        os.path.join(project_root, 'templates'),
        os.path.join(project_root, 'plugins')
    ]

    app = Flask(__name__,
                template_folder=template_folders[0],
                static_folder=os.path.join(project_root, 'static'))

    # 添加插件模板路径
    from jinja2 import FileSystemLoader, ChoiceLoader

    # 创建多路径模板加载器
    template_loaders = []
    for folder in template_folders:
        if os.path.exists(folder):
            template_loaders.append(FileSystemLoader(folder))

    # 添加插件模板目录
    plugins_dir = os.path.join(project_root, 'plugins')
    if os.path.exists(plugins_dir):
        for plugin_name in os.listdir(plugins_dir):
            plugin_template_dir = os.path.join(plugins_dir, plugin_name, 'templates')
            if os.path.exists(plugin_template_dir):
                template_loaders.append(FileSystemLoader(plugin_template_dir))

    app.jinja_loader = ChoiceLoader(template_loaders)
    
    # 加载配置
    import sys
    config_path = os.path.dirname(os.path.dirname(__file__))
    if config_path not in sys.path:
        sys.path.append(config_path)
    from config import config
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化扩展
    db.init_app(app)
    
    # 初始化核心管理器
    log_manager = LogManager(app)
    plugin_manager = PluginManager(app)
    route_registry = RouteRegistry(app)
    api_manager = APIManager(app)
    
    # 将管理器绑定到应用实例
    app.log_manager = log_manager
    app.plugin_manager = plugin_manager
    app.route_registry = route_registry
    app.api_manager = api_manager
    
    # 注册核心路由
    register_core_routes(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册模板上下文处理器
    register_template_context(app)
    
    # 应用启动后的初始化
    with app.app_context():
        try:
            # 创建数据库表
            db.create_all()

            # 加载插件
            loadPlugins(app)

            app.logger.info("应用初始化完成")

        except Exception as e:
            app.logger.error(f"应用初始化失败: {str(e)}")
            
    return app


def register_core_routes(app: Flask):
    """注册核心路由"""
    
    @app.route('/')
    def index():
        """首页"""
        try:
            # 获取已加载的插件信息
            plugins = []
            if hasattr(app, 'plugin_manager'):
                pluginsInfo = app.plugin_manager.getAllPluginInfo()
                for pluginInfo in pluginsInfo:
                    plugin = app.plugin_manager.getPlugin(pluginInfo['name'])
                    if plugin:
                        navItems = plugin.getNavItems() if hasattr(plugin, 'getNavItems') else []
                        pluginInfo['navItems'] = navItems
                        plugins.append(pluginInfo)

            return render_template('index.html', plugins=plugins)

        except Exception as e:
            app.logger.error(f"加载首页失败: {str(e)}")
            return render_template('index.html', plugins=[], error=str(e))
            
    @app.route('/health')
    def health_check():
        """健康检查"""
        return jsonify({
            'status': 'healthy',
            'message': '集成工具平台运行正常'
        })

    @app.route('/static/plugins/<plugin_name>/<path:filename>')
    def plugin_static(plugin_name, filename):
        """处理插件静态文件"""
        try:
            # 构建插件静态文件路径
            project_root = os.path.dirname(os.path.dirname(__file__))
            plugin_static_dir = os.path.join(project_root, 'plugins', plugin_name, 'static')

            # 检查插件静态文件目录是否存在
            if not os.path.exists(plugin_static_dir):
                app.logger.warning(f"插件 {plugin_name} 的静态文件目录不存在: {plugin_static_dir}")
                abort(404)

            # 检查文件是否存在
            file_path = os.path.join(plugin_static_dir, filename)
            if not os.path.exists(file_path):
                app.logger.warning(f"插件静态文件不存在: {file_path}")
                abort(404)

            # 安全检查：确保文件在插件目录内
            if not os.path.abspath(file_path).startswith(os.path.abspath(plugin_static_dir)):
                app.logger.warning(f"非法的文件路径访问: {file_path}")
                abort(403)

            return send_from_directory(plugin_static_dir, filename)

        except Exception as e:
            app.logger.error(f"处理插件静态文件失败: {str(e)}")
            abort(500)

    app.logger.info("核心路由注册完成")


def register_error_handlers(app: Flask):
    """注册错误处理器"""
    
    @app.errorhandler(404)
    def not_found(error):
        """404错误处理"""
        if '/api/' in str(error):
            return jsonify({
                'success': False,
                'message': '接口不存在',
                'error': 'Not Found'
            }), 404
        return render_template('error.html', error_code=404, error_message='页面不存在'), 404
        
    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理"""
        app.logger.error(f"内部服务器错误: {str(error)}")
        if '/api/' in str(error):
            return jsonify({
                'success': False,
                'message': '内部服务器错误',
                'error': 'Internal Server Error'
            }), 500
        return render_template('error.html', error_code=500, error_message='内部服务器错误'), 500
        
    @app.errorhandler(Exception)
    def handle_exception(error):
        """通用异常处理"""
        app.logger.error(f"未处理的异常: {str(error)}")
        if '/api/' in str(error):
            return jsonify({
                'success': False,
                'message': '服务器异常',
                'error': str(error)
            }), 500
        return render_template('error.html', error_code=500, error_message='服务器异常'), 500
        
    app.logger.info("错误处理器注册完成")


def register_template_context(app: Flask):
    """注册模板上下文处理器"""
    
    @app.context_processor
    def injectGlobalVars():
        """注入全局模板变量"""
        return {
            'app_name': '集成工具平台',
            'app_version': '2.0.0',
            'plugin_count': len(app.plugin_manager.getAllPlugins()) if hasattr(app, 'plugin_manager') else 0
        }

    @app.context_processor
    def injectNavigation():
        """注入导航信息"""
        navItems = []

        if hasattr(app, 'plugin_manager'):
            plugins = app.plugin_manager.getAllPlugins()
            for pluginName, plugin in plugins.items():
                pluginNavItems = plugin.getNavItems() if hasattr(plugin, 'getNavItems') else []
                for navItem in pluginNavItems:
                    navItem['plugin'] = pluginName
                    navItems.append(navItem)

        return {
            'plugin_nav_items': navItems
        }
        
    app.logger.info("模板上下文处理器注册完成")


def loadPlugins(app: Flask):
    """加载插件"""
    try:
        if not hasattr(app, 'plugin_manager'):
            app.logger.error("插件管理器未初始化")
            return

        pluginManager = app.plugin_manager
        routeRegistry = app.route_registry
        apiManager = app.api_manager

        # 发现并加载所有插件
        availablePlugins = pluginManager.discover_plugins()
        app.logger.info(f"发现 {len(availablePlugins)} 个可用插件")

        loadedCount = 0
        for pluginName in availablePlugins:
            try:
                # 加载插件
                if pluginManager.loadPlugin(pluginName):
                    plugin = pluginManager.getPlugin(pluginName)

                    if plugin:
                        # 注册插件路由
                        pluginRoutes = plugin.getPluginRoutes()
                        if pluginRoutes:
                            routeRegistry.register_plugin_routes(pluginName, pluginRoutes)

                        # 注册插件API
                        pluginApis = plugin.getPluginApiRoutes()
                        if pluginApis:
                            apiManager.register_plugin_apis(pluginName, pluginApis)

                        loadedCount += 1
                        app.logger.info(f"插件加载成功: {pluginName}")
                    else:
                        app.logger.error(f"插件实例获取失败: {pluginName}")

                else:
                    app.logger.error(f"插件加载失败: {pluginName}")

            except Exception as e:
                app.logger.error(f"加载插件时发生异常: {pluginName}, 错误: {str(e)}")

        app.logger.info(f"插件加载完成，成功加载 {loadedCount}/{len(availablePlugins)} 个插件")

    except Exception as e:
        app.logger.error(f"插件加载过程失败: {str(e)}")


def shutdown_app(app: Flask):
    """关闭应用"""
    try:
        if hasattr(app, 'plugin_manager'):
            # 卸载所有插件
            plugin_names = app.plugin_manager.get_loaded_plugin_names()
            for plugin_name in plugin_names:
                app.plugin_manager.unload_plugin(plugin_name)
                
        if hasattr(app, 'route_registry'):
            # 清理所有路由
            app.route_registry.clear_all_routes()
            
        if hasattr(app, 'api_manager'):
            # 清理所有API
            app.api_manager.clear_all_apis()
            
        if hasattr(app, 'log_manager'):
            # 清理日志记录器
            app.log_manager.clear_plugin_loggers()
            
        app.logger.info("应用关闭完成")
        
    except Exception as e:
        app.logger.error(f"应用关闭失败: {str(e)}")


# 为了向后兼容，保留一些常用的导入
__all__ = ['create_app', 'db', 'shutdown_app']
