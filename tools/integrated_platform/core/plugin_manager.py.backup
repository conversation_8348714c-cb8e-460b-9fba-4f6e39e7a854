#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插件管理器
"""

import os
import sys
import importlib
import importlib.util
from typing import Dict, List, Any, Optional, Type
import logging
from pathlib import Path

from .base_plugin import BasePlugin, PluginError, PluginInitError


class PluginManager:
    """插件管理器"""
    
    def __init__(self, app=None, plugins_dir: str = None):
        self.app = app
        self.plugins_dir = plugins_dir or os.path.join(os.path.dirname(os.path.dirname(__file__)), 'plugins')
        self.logger = logging.getLogger('plugin_manager')
        
        # 插件存储
        self._plugins: Dict[str, BasePlugin] = {}
        self._plugin_configs: Dict[str, Dict[str, Any]] = {}
        self._plugin_modules: Dict[str, Any] = {}
        
        # 确保插件目录存在
        os.makedirs(self.plugins_dir, exist_ok=True)
        
    def discover_plugins(self) -> List[str]:
        """发现可用的插件"""
        plugins = []
        
        if not os.path.exists(self.plugins_dir):
            self.logger.warning(f"插件目录不存在: {self.plugins_dir}")
            return plugins
            
        for item in os.listdir(self.plugins_dir):
            plugin_path = os.path.join(self.plugins_dir, item)
            
            # 跳过非目录和隐藏目录
            if not os.path.isdir(plugin_path) or item.startswith('.'):
                continue
                
            # 跳过__pycache__目录
            if item == '__pycache__':
                continue
                
            # 检查是否有plugin.py文件
            plugin_file = os.path.join(plugin_path, 'plugin.py')
            if os.path.exists(plugin_file):
                plugins.append(item)
                self.logger.debug(f"发现插件: {item}")
                
        self.logger.info(f"发现 {len(plugins)} 个插件: {plugins}")
        return plugins
        
    def load_plugin(self, plugin_name: str, config: Dict[str, Any] = None) -> bool:
        """加载插件"""
        try:
            if plugin_name in self._plugins:
                self.logger.warning(f"插件已加载: {plugin_name}")
                return True
                
            # 构建插件路径
            plugin_path = os.path.join(self.plugins_dir, plugin_name)
            plugin_file = os.path.join(plugin_path, 'plugin.py')
            
            if not os.path.exists(plugin_file):
                raise PluginError(f"插件文件不存在: {plugin_file}")
                
            # 动态导入插件模块
            spec = importlib.util.spec_from_file_location(f"plugins.{plugin_name}", plugin_file)
            if spec is None or spec.loader is None:
                raise PluginError(f"无法加载插件规范: {plugin_name}")
                
            module = importlib.util.module_from_spec(spec)
            self._plugin_modules[plugin_name] = module
            
            # 添加到sys.modules以支持相对导入
            sys.modules[f"plugins.{plugin_name}"] = module
            spec.loader.exec_module(module)
            
            # 获取插件类
            if not hasattr(module, 'Plugin'):
                raise PluginError(f"插件模块缺少Plugin类: {plugin_name}")
                
            plugin_class = getattr(module, 'Plugin')
            if not issubclass(plugin_class, BasePlugin):
                raise PluginError(f"插件类必须继承BasePlugin: {plugin_name}")
                
            # 创建插件实例
            plugin_instance = plugin_class(self.app)
            
            # 处理配置
            if config is None:
                config = plugin_instance.get_default_config()
            else:
                # 合并默认配置
                default_config = plugin_instance.get_default_config()
                merged_config = {**default_config, **config}
                config = merged_config
                
            # 验证配置
            if not plugin_instance.validate_config(config):
                raise PluginError(f"插件配置验证失败: {plugin_name}")
                
            # 处理配置
            processed_config = plugin_instance.process_config(config)
            self._plugin_configs[plugin_name] = processed_config
            
            # 初始化插件
            if not plugin_instance.initialize():
                raise PluginInitError(f"插件初始化失败: {plugin_name}")
                
            # 存储插件实例
            self._plugins[plugin_name] = plugin_instance
            
            self.logger.info(f"插件加载成功: {plugin_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载插件失败: {plugin_name}, 错误: {str(e)}")
            # 清理失败的加载
            self._cleanup_failed_plugin(plugin_name)
            return False
            
    def unload_plugin(self, plugin_name: str) -> bool:
        """卸载插件"""
        try:
            if plugin_name not in self._plugins:
                self.logger.warning(f"插件未加载: {plugin_name}")
                return True
                
            plugin = self._plugins[plugin_name]
            
            # 清理插件
            if not plugin.cleanup():
                self.logger.warning(f"插件清理失败: {plugin_name}")
                
            # 移除插件
            del self._plugins[plugin_name]
            if plugin_name in self._plugin_configs:
                del self._plugin_configs[plugin_name]
            if plugin_name in self._plugin_modules:
                del self._plugin_modules[plugin_name]
                
            # 从sys.modules中移除
            module_name = f"plugins.{plugin_name}"
            if module_name in sys.modules:
                del sys.modules[module_name]
                
            self.logger.info(f"插件卸载成功: {plugin_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"卸载插件失败: {plugin_name}, 错误: {str(e)}")
            return False
            
    def reload_plugin(self, plugin_name: str, config: Dict[str, Any] = None) -> bool:
        """重新加载插件"""
        self.logger.info(f"正在重新加载插件: {plugin_name}")
        
        # 保存当前配置
        if config is None and plugin_name in self._plugin_configs:
            config = self._plugin_configs[plugin_name]
            
        # 卸载插件
        if not self.unload_plugin(plugin_name):
            return False
            
        # 重新加载插件
        return self.load_plugin(plugin_name, config)
        
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """获取插件实例"""
        return self._plugins.get(plugin_name)
        
    def get_all_plugins(self) -> Dict[str, BasePlugin]:
        """获取所有已加载的插件"""
        return self._plugins.copy()
        
    def get_plugin_config(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """获取插件配置"""
        return self._plugin_configs.get(plugin_name)
        
    def update_plugin_config(self, plugin_name: str, config: Dict[str, Any]) -> bool:
        """更新插件配置"""
        if plugin_name not in self._plugins:
            self.logger.error(f"插件未加载: {plugin_name}")
            return False
            
        plugin = self._plugins[plugin_name]
        
        # 验证配置
        if not plugin.validate_config(config):
            self.logger.error(f"插件配置验证失败: {plugin_name}")
            return False
            
        # 处理配置
        processed_config = plugin.process_config(config)
        self._plugin_configs[plugin_name] = processed_config
        
        self.logger.info(f"插件配置更新成功: {plugin_name}")
        return True
        
    def get_plugin_info(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """获取插件信息"""
        plugin = self.get_plugin(plugin_name)
        if plugin:
            return plugin.get_info()
        return None
        
    def get_all_plugin_info(self) -> List[Dict[str, Any]]:
        """获取所有插件信息"""
        return [plugin.get_info() for plugin in self._plugins.values()]
        
    def get_plugin_status(self, plugin_name: str) -> Optional[Dict[str, Any]]:
        """获取插件状态"""
        plugin = self.get_plugin(plugin_name)
        if plugin:
            return plugin.get_status()
        return None
        
    def get_all_plugin_status(self) -> List[Dict[str, Any]]:
        """获取所有插件状态"""
        return [plugin.get_status() for plugin in self._plugins.values()]
        
    def is_plugin_loaded(self, plugin_name: str) -> bool:
        """检查插件是否已加载"""
        return plugin_name in self._plugins
        
    def load_all_plugins(self, plugin_configs: Dict[str, Dict[str, Any]] = None) -> Dict[str, bool]:
        """加载所有可用插件"""
        if plugin_configs is None:
            plugin_configs = {}
            
        results = {}
        available_plugins = self.discover_plugins()
        
        for plugin_name in available_plugins:
            config = plugin_configs.get(plugin_name)
            results[plugin_name] = self.load_plugin(plugin_name, config)
            
        return results
        
    def _cleanup_failed_plugin(self, plugin_name: str):
        """清理加载失败的插件"""
        try:
            if plugin_name in self._plugins:
                del self._plugins[plugin_name]
            if plugin_name in self._plugin_configs:
                del self._plugin_configs[plugin_name]
            if plugin_name in self._plugin_modules:
                del self._plugin_modules[plugin_name]
                
            module_name = f"plugins.{plugin_name}"
            if module_name in sys.modules:
                del sys.modules[module_name]
        except Exception as e:
            self.logger.error(f"清理失败插件时出错: {plugin_name}, 错误: {str(e)}")
            
    def get_loaded_plugin_names(self) -> List[str]:
        """获取已加载插件名称列表"""
        return list(self._plugins.keys())
        
    def get_plugin_count(self) -> int:
        """获取已加载插件数量"""
        return len(self._plugins)
