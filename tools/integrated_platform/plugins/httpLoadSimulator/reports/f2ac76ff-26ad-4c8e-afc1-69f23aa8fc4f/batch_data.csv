测试项目,HTTP负载
登录接口测试,"POST /api/login HTTP/1.1
Host: example.com
Content-Type: application/json
Content-Length: 45

{""username"": ""test"", ""password"": ""123456""}"
用户信息查询,"GET /api/user/profile HTTP/1.1
Host: example.com
Authorization: Bearer token123
Accept: application/json"
数据提交测试,"POST /api/data HTTP/1.1
Host: example.com
Content-Type: application/x-www-form-urlencoded
Content-Length: 25

name=test&value=example"
