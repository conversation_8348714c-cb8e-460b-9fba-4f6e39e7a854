# -*- coding: utf-8 -*-
"""
HTTP负载模拟访问核心功能模块
"""

import os
import socket
import subprocess
import threading
import time
import signal
import requests
import re
from urllib.parse import urlparse
from typing import List, Dict, Any, Optional, Tuple
import logging
from socketserver import ThreadingTCPServer,  BaseRequestHandler, ThreadingMixIn
from http.server import HTTPServer, BaseHTTPRequestHandler
import random
import ipaddress
import shlex

class ThreadingHTTPServer(ThreadingMixIn, HTTPServer):
    """支持多线程的HTTP服务器，改善连接处理"""
    allow_reuse_address = True
    daemon_threads = True

    def __init__(self, server_address, RequestHandlerClass):
        super().__init__(server_address, RequestHandlerClass)
        # 设置socket选项以改善连接处理
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        # 设置TCP_NODELAY以减少延迟
        self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)


class ImprovedThreadingTCPServer(ThreadingTCPServer):
    """改进的多线程TCP服务器，支持更好的连接处理"""
    allow_reuse_address = True
    daemon_threads = True

    def __init__(self, server_address, RequestHandlerClass):
        super().__init__(server_address, RequestHandlerClass)
        # 设置socket选项以改善连接处理
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        # 设置TCP_NODELAY以减少延迟
        self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        # 设置SO_LINGER以确保连接正确关闭
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_LINGER, struct.pack('ii', 1, 0))


class MockHTTPHandler(BaseHTTPRequestHandler):
    """模拟HTTP服务器处理器"""

    def do_GET(self):
        """处理GET请求"""
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Server', 'NetworkSimulator/1.0')
        self.send_header('Connection', 'close')  # 明确指示关闭连接
        self.end_headers()

        response_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>模拟服务器响应</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>网络模拟访问 - 服务器响应</h1>
            <p>请求路径: {self.path}</p>
            <p>请求方法: {self.command}</p>
            <p>客户端地址: {self.client_address[0]}:{self.client_address[1]}</p>
            <p>时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <hr>
            <p>这是一个模拟的HTTP服务器响应，用于网络流量分析。</p>
        </body>
        </html>
        """.encode('utf-8')

        self.wfile.write(response_body)
        # 确保数据发送完毕后关闭连接
        self.wfile.flush()

    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Server', 'NetworkSimulator/1.0')
        self.send_header('Connection', 'close')  # 明确指示关闭连接
        self.end_headers()

        response_data = {
            'status': 'success',
            'message': '请求处理成功',
            'path': self.path,
            'method': self.command,
            'client_ip': self.client_address[0],
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'received_data_length': len(post_data)
        }

        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
        # 确保数据发送完毕后关闭连接
        self.wfile.flush()

    def finish(self):
        """完成请求处理，确保连接正确关闭"""
        try:
            # 确保所有数据都已发送
            if hasattr(self, 'wfile') and self.wfile:
                self.wfile.flush()
            # 调用父类的finish方法
            super().finish()
        except Exception:
            # 忽略关闭时的异常
            pass

    def handle(self):
        """重写handle方法，确保连接管理正确"""
        try:
            super().handle()
        except Exception:
            # 忽略处理异常，确保连接能够正确关闭
            pass
        finally:
            # 确保socket连接关闭
            try:
                if hasattr(self, 'connection') and self.connection:
                    self.connection.close()
            except Exception:
                pass

    def log_message(self, format, *args):
        """重写日志方法，避免输出到stderr"""
        pass


class MockTCPHandler(BaseRequestHandler):
    """模拟TCP服务器处理器"""

    def setup(self):
        """设置连接"""
        try:
            # 设置socket选项以改善连接处理
            self.request.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            self.request.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            # 设置接收超时
            self.request.settimeout(30.0)
        except Exception:
            pass

    def handle(self):
        """处理TCP连接"""
        try:
            # 接收客户端数据
            data = self.request.recv(1024)
            if not data:
                return  # 客户端已关闭连接

            # 检测是否收到FIN（连接关闭信号）
            if len(data) == 0:
                return

            # 模拟HTTP响应
            if data.startswith(b'GET') or data.startswith(b'POST'):
                response_body = (
                    b"TCP Mock Server Response\n"
                    b"Received data length: " + str(len(data)).encode() + b"\n"
                    b"Timestamp: " + time.strftime('%Y-%m-%d %H:%M:%S').encode() + b"\n"
                )
                response = (
                    b"HTTP/1.1 200 OK\r\n"
                    b"Content-Type: text/plain\r\n"
                    b"Server: NetworkSimulator-TCP/1.0\r\n"
                    b"Connection: close\r\n"
                    b"Content-Length: " + str(len(response_body)).encode() + b"\r\n"
                    b"\r\n" + response_body
                )
            else:
                # 普通TCP响应
                response = (
                    b"TCP-ECHO: " + data +
                    b"\nServer: NetworkSimulator-TCP/1.0\n" +
                    b"Timestamp: " + time.strftime('%Y-%m-%d %H:%M:%S').encode() + b"\n"
                )

            # 发送响应数据
            self.request.sendall(response)

        except socket.timeout:
            # 超时，正常关闭连接
            pass
        except ConnectionResetError:
            # 客户端重置连接
            pass
        except Exception:
            # 其他异常，忽略
            pass

    def finish(self):
        """完成连接处理，确保连接正确关闭"""
        try:
            # 优雅关闭写端，发送FIN
            self.request.shutdown(socket.SHUT_WR)
        except (OSError, AttributeError):
            # 忽略已关闭的socket错误
            pass
        finally:
            # 确保连接完全关闭
            try:
                self.request.close()
            except Exception:
                pass


class HttpLoadSimulator:
    """HTTP负载模拟器"""
    
    def __init__(self, plugin_dir: str, logger: logging.Logger):
        self.pluginDir = plugin_dir
        self.logger = logger
        self.tcpdumpProcess = None
        self.tcpdumpLock = threading.Lock()
        self.mockServers = {}  # 存储启动的模拟服务器
        self.serverLock = threading.Lock()
    
    def _findAvailablePort(self, start_port: int) -> int:
        """查找可用端口"""
        import socket

        for port in range(start_port, start_port + 100):  # 尝试100个端口
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue

        # 如果都不可用，返回一个随机端口
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', 0))
            return s.getsockname()[1]


    def simulateHttpLoad(self, http_payload: str, target_ip: str, target_port: int, output_dir: str, pcap_filename_prefix: str = None) -> Dict[str, Any]:
        """
        模拟HTTP负载访问并抓包

        Args:
            http_payload: HTTP负载内容（完整的HTTP请求）
            target_ip: 目标IP地址
            target_port: 目标端口
            output_dir: 输出pcap文件目录
            pcap_filename_prefix: pcap文件名前缀（可选）

        Returns:
            Dict: 执行结果
        """
        try:
            self.logger.info(f"开始HTTP负载模拟，目标: {target_ip}:{target_port}")

            pcapProcess=pcapReplay(self.logger)

            # 启动HTTP模拟服务器，使用动态端口避免冲突
            mock_port = self._findAvailablePort(target_port)
            mock_server = self._startHttpMockServer({target_ip}, mock_port)
            if not mock_server:
                return {
                    'success': False,
                    'message': 'HTTP模拟服务器启动失败'
                }


            # 解析HTTP负载
            parsed_request = self._parseHttpPayload(http_payload)
            if not parsed_request:
                if pcap_filename_prefix:
                    self.logger.error(f"HTTP负载解析失败，测试项目: {pcap_filename_prefix}")
                else:
                    self.logger.error("HTTP负载解析失败")
                return {
                    'success': False,
                    'message': 'HTTP负载解析失败，请检查格式是否正确'
                }
            self.logger.info("HTTP负载解析成功")

            # 创建抓包文件路径
            if pcap_filename_prefix:
                pcap_file = os.path.join(output_dir, f"{pcap_filename_prefix}.pcap")
            else:
                pcap_file = os.path.join(output_dir, f"http_load_{target_ip}_{mock_port}.pcap")

            # 启动tcpdump抓包
            if not self._startTcpdump(mock_port, pcap_file):
                return {
                    'success': False,
                    'message': 'tcpdump抓包启动失败'
                }

            # 等待tcpdump启动
            time.sleep(2)

            # 执行HTTP请求
            result = self._sendHttpRequest(parsed_request, target_ip, mock_port)

            # 等待网络交互完成
            time.sleep(2)

            # 停止tcpdump
            self._stopTcpdump()
            if mock_server:
                mock_server.shutdown()
                mock_server.server_close()
            time.sleep(2)

            # 检查抓包文件
            if os.path.exists(pcap_file) and os.path.getsize(pcap_file) > 0:
                self.logger.info(f"抓包文件已生成: {pcap_file}")
                # 使用随机IP
                srcIp = pcapProcess.randomIpAddress()
                dstIp = pcapProcess.randomIpAddress()
                cache,newPcap=pcapProcess.rewrite(srcIp=srcIp, dstIp=dstIp,pcapPath=pcap_file,port=mock_port)
                if cache and os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                if newPcap and os.path.exists(pcap_file):
                        try:
                            os.remove(pcap_file)
                            os.rename(newPcap,pcap_file)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup old pcap file {cache}: {str(cleanup_e)}")
                return {
                    'success': True,
                    'message': f'HTTP负载模拟完成，成功发送请求到 {target_ip}:{target_port}，响应: {result.get("response", "无响应")}',
                    'details': {
                        'target_ip': target_ip,
                        'target_port': target_port,
                        'method': parsed_request.get('method', 'UNKNOWN'),
                        'path': parsed_request.get('path', '/'),
                        'response_status': result.get('status_code', 'N/A'),
                        'pcap_file': pcap_file
                    }
                }
            else:
                self.logger.error(f"抓包文件生成失败或为空")
                return {
                    'success': False,
                    'message': '抓包文件生成失败或为空'
                }

        except Exception as e:
            self.logger.error(f"HTTP负载模拟失败: {str(e)}")
            self._stopTcpdump()
            if mock_server:
                mock_server.shutdown()
                mock_server.server_close()
            return {
                'success': False,
                'message': f'HTTP负载模拟失败: {str(e)}'
            }

    def _parseHttpPayload(self, http_payload: str) -> Optional[Dict[str, Any]]:
        """
        解析HTTP负载内容

        Args:
            http_payload: HTTP负载字符串

        Returns:
            Dict: 解析后的HTTP请求信息
        """
        try:
            lines = http_payload.strip().split('\n')
            if not lines:
                return None

            # 解析请求行
            request_line = lines[0].strip()
            parts = request_line.split(' ')
            if len(parts) < 3:
                return None

            method = parts[0]
            path = parts[1]
            version = parts[2]

            # 解析请求头
            headers = {}
            body_start = 1
            for i, line in enumerate(lines[1:], 1):
                line = line.strip()
                if not line:  # 空行表示头部结束
                    body_start = i + 1
                    break
                
                if ':' in line:
                    key, value = line.split(':', 1)
                    headers[key.strip()] = value.strip()

            # 解析请求体
            body = ''
            if body_start < len(lines):
                body = '\n'.join(lines[body_start:]).strip()

            return {
                'method': method,
                'path': path,
                'version': version,
                'headers': headers,
                'body': body
            }

        except Exception as e:
            self.logger.error(f"解析HTTP负载失败: {str(e)}")
            return None

    def _sendHttpRequest(self, parsed_request: Dict[str, Any], target_ip: str, target_port: int) -> Dict[str, Any]:
        """
        发送HTTP请求

        Args:
            parsed_request: 解析后的HTTP请求
            target_ip: 目标IP地址
            target_port: 目标端口

        Returns:
            Dict: 请求结果
        """
        try:
            method = parsed_request.get('method', 'GET')
            path = parsed_request.get('path', '/')
            headers = parsed_request.get('headers', {})
            body = parsed_request.get('body', '')

            # 构建URL
            url = f"http://{target_ip}:{target_port}{path}"

            # 准备请求参数
            request_kwargs = {
                'headers': headers,
                'timeout': 10,
                'allow_redirects': False
            }

            # 如果有请求体，添加到请求中
            if body:
                if method.upper() in ['POST', 'PUT', 'PATCH']:
                    # 根据Content-Type处理请求体
                    content_type = headers.get('Content-Type', '').lower()
                    if 'application/json' in content_type:
                        request_kwargs['json'] = body
                    elif 'application/x-www-form-urlencoded' in content_type:
                        request_kwargs['data'] = body
                    else:
                        request_kwargs['data'] = body

            # 发送请求
            response = requests.request(method, url, **request_kwargs)
            self.logger.info(f"Request to {url} completed with status code {response.status_code}")

            return {
                'success': True,
                'status_code': response.status_code,
                'response': f"HTTP {response.status_code} {response.reason}",
                'headers': dict(response.headers),
                'content_length': len(response.content)
            }

        except requests.exceptions.RequestException as e:
            # 即使请求失败，也算成功，因为生成了网络流量
            return {
                'success': True,
                'status_code': 'N/A',
                'response': f"网络交互完成（连接异常: {str(e)}）",
                'note': '模拟HTTP负载交互'
            }
        except Exception as e:
            self.logger.error(f"发送HTTP请求失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _startTcpdump(self,port,output_file: str) -> bool:
        """启动tcpdump抓包"""
        try:
            with self.tcpdumpLock:
                if self.tcpdumpProcess:
                    self._stopTcpdump()

                
                # 构建tcpdump命令
                cmd = [
                    'tcpdump',
                    '-i', 'lo',  # 监听lo接口
                    '-w', output_file,  # 输出文件
                    '-s', '0',  # 抓取完整数据包
                    'src host','127.0.0.1',
                    'and','dst host','127.0.0.1',
                    'and','port', f'{port}'  #抓取指定端口的数据包
                ]

                self.logger.info(f"启动tcpdump命令: {' '.join(cmd)}")

                # 启动tcpdump进程
                self.tcpdumpProcess = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )

                return True

        except Exception as e:
            self.logger.error(f"启动tcpdump失败: {str(e)}")
            # 如果tcpdump启动失败，创建模拟文件
            return self._createMockPcapFile(output_file)

    def _stopTcpdump(self):
        """停止tcpdump抓包"""
        try:
            with self.tcpdumpLock:
                if self.tcpdumpProcess:
                    # 发送SIGTERM信号终止进程组
                    os.killpg(os.getpgid(self.tcpdumpProcess.pid), signal.SIGTERM)
                    
                    # 等待进程结束
                    try:
                        self.tcpdumpProcess.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        # 如果进程没有在5秒内结束，强制杀死
                        os.killpg(os.getpgid(self.tcpdumpProcess.pid), signal.SIGKILL)
                        self.tcpdumpProcess.wait()
                    
                    self.logger.info("tcpdump已停止")
                    self.tcpdumpProcess = None
                    
        except Exception as e:
            self.logger.error(f"停止tcpdump失败: {str(e)}")
            self.tcpdumpProcess = None

    def _createMockPcapFile(self, output_file: str) -> bool:
        """创建模拟的pcap文件（当tcpdump不可用时）"""
        try:
            # 创建一个最小的pcap文件头
            # pcap文件格式的基本头部（24字节）
            pcap_header = bytearray([
                0xD4, 0xC3, 0xB2, 0xA1,  # 魔数
                0x02, 0x00,              # 版本主号
                0x04, 0x00,              # 版本次号
                0x00, 0x00, 0x00, 0x00,  # 时区偏移
                0x00, 0x00, 0x00, 0x00,  # 时间戳精度
                0xFF, 0xFF, 0x00, 0x00,  # 最大包长度
                0x01, 0x00, 0x00, 0x00   # 数据链路类型（以太网）
            ])

            with open(output_file, 'wb') as f:
                f.write(pcap_header)

            self.logger.info(f"创建模拟pcap文件: {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"创建模拟pcap文件失败: {str(e)}")
            return False

    def _startMockServers(self, targets: List[str], protocol: str, port: int) -> List[Dict[str, Any]]:
            """启动模拟服务器"""
            mock_servers = []

            try:
                with self.serverLock:
                    #for i, target in enumerate(targets):
                    mock_port = port
                    target='127.0.0.1'

                    if protocol.lower() in ['http', 'https']:
                            # 启动HTTP模拟服务器
                            server = self._startHttpMockServer(target, mock_port)
                            if server:
                                mock_servers.append({
                                    'target': target,
                                    'protocol': protocol,
                                    'original_port': port,
                                    'mock_port': mock_port,
                                    'server': server,
                                    'type': 'http'
                                })
                                self.logger.info(f"HTTP模拟服务器启动成功: {target} -> 127.0.0.1:{mock_port}")

                    elif protocol.lower() == 'tcp':
                            # 启动TCP模拟服务器
                            server = self._startTcpMockServer(target, mock_port)
                            if server:
                                mock_servers.append({
                                    'target': target,
                                    'protocol': protocol,
                                    'original_port': port,
                                    'mock_port': mock_port,
                                    'server': server,
                                    'type': 'tcp'
                                })
                                self.logger.info(f"TCP模拟服务器启动成功: {target} -> 127.0.0.1:{mock_port}")

                    elif protocol.lower() == 'udp':
                            # UDP不需要持续的服务器，在访问时直接模拟
                            mock_servers.append({
                                'target': target,
                                'protocol': protocol,
                                'original_port': port,
                                'mock_port': mock_port,
                                'server': None,
                                'type': 'udp'
                            })
                            self.logger.info(f"UDP模拟配置: {target} -> 127.0.0.1:{mock_port}")

                    elif protocol.lower() == 'dns':
                            # 启动DNS模拟服务器
                            server = self._startDnsMockServer(target, mock_port)
                            if server:
                                mock_servers.append({
                                    'target': target,
                                    'protocol': protocol,
                                    'original_port': port,
                                    'mock_port': mock_port,
                                    'server': server,
                                    'type': 'dns'
                                })
                                self.logger.info(f"DNS模拟服务器启动成功: {target} -> 127.0.0.1:{mock_port}")
                        

                    return mock_servers

            except Exception as e:
                self.logger.error(f"启动模拟服务器失败: {str(e)}")
                self._stopMockServers(mock_servers)
                return []

    def _startHttpMockServer(self, target: str, port: int) -> Optional[ThreadingHTTPServer]:
        """启动HTTP模拟服务器"""
        try:
            server = ThreadingHTTPServer(('127.0.0.1', port), MockHTTPHandler)
            # 设置服务器属性以改善连接处理
            server.timeout = 30  # 设置超时时间

            server_thread = threading.Thread(target=server.serve_forever, daemon=True)
            server_thread.start()

            self.logger.info(f"HTTP模拟服务器启动成功: 127.0.0.1:{port}")
            return server
        except Exception as e:
            self.logger.error(f"启动HTTP模拟服务器失败 {target}:{port} - {str(e)}")
            return None

    def _startTcpMockServer(self, target: str, port: int) -> Optional[ImprovedThreadingTCPServer]:
        """启动TCP模拟服务器"""
        try:
            server = ImprovedThreadingTCPServer(('127.0.0.1', port), MockTCPHandler)
            # 设置服务器属性以改善连接处理
            server.timeout = 30  # 设置超时时间

            server_thread = threading.Thread(target=server.serve_forever, daemon=True)
            server_thread.start()
            self.logger.info(f"TCP模拟服务器启动成功: 127.0.0.1:{port}")
            return server
        except Exception as e:
            self.logger.error(f"启动TCP模拟服务器失败 {target}:{port} - {str(e)}")
            return None

    def _stopMockServers(self, mock_servers: List[Dict[str, Any]]):
        """停止模拟服务器"""
        try:
            with self.serverLock:
                for server_info in mock_servers:
                    server = server_info.get('server')
                    if server:
                        try:
                            server.shutdown()
                            server.server_close()
                            self.logger.info(f"模拟服务器已停止: {server_info['target']} ({server_info['type']})")
                        except Exception as e:
                            self.logger.error(f"停止模拟服务器失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"停止模拟服务器失败: {str(e)}")

class pcapReplay(object):
    '''报文回放到DUT'''
    def __init__(self,logger, task_id=None):
        self.name = 'pcap_replay'
        self.pacps = []
        self.task_id = task_id
        self.logger = logger
        
    
    def rewrite(self, **kwargs):
        try:
            # 参数验证
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']
            port= kwargs.get('port', 80)

            # 验证输入文件是否存在
            if not os.path.exists(pcapPath):
                raise FileNotFoundError(f"Input pcap file not found: {pcapPath}")

            dstMac = '00:1C:00:00:00:01'
            srcMac1 = '10:1C:12:B8:58:C2'
            srcMac2 = '01:1C:00:21:DE:DB'

            # 安全地处理文件路径
            if '.' not in pcapPath:
                self.logger.error(f"Invalid pcap file path format: {pcapPath}")
                return None, None

            pcapName = pcapPath[:pcapPath.rfind('.')]
            cache = pcapName + '.cache'
            outputfile = pcapName + '_out.pcap'
            PATH=os.path.dirname(os.path.abspath(__file__))
            fragConf=os.path.join(PATH, "uploads", "frag.conf") # 定义报文分片的配置文件，规定了tcprewrite时对报文分片的大小
            service = os.path.join(PATH, "uploads", "service.conf")
            with open(service, 'w') as f:
                f.write(f'http   {port}/tcp')
            # 执行tcprewrite命令修复报文小于最低长度字节的问题
            tcprewrite = f'tcprewrite --fixlen=pad  --fixcsum  -i {shlex.quote(pcapPath)} -o {shlex.quote(pcapPath)}'
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None, None

            # 执行tcpprep命令，基于服务器端口进行分类
            tcpprep = f'tcpprep -p -s {service} -i {shlex.quote(pcapPath)} -o {shlex.quote(cache)}'
            try:
                r = subprocess.getstatusoutput(tcpprep)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")

                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcpprep command: {str(e)}")
                return None, None

            # 执行tcprewrite命令
            tcprewrite = f'tcprewrite --fixlen=pad  --fixcsum  --fragroute={fragConf} --enet-dmac={dstMac}:{dstMac} --enet-smac={srcMac1}:{srcMac2} --endpoints={srcIp}:{dstIp} -i {shlex.quote(pcapPath)} -c {shlex.quote(cache)} -o {shlex.quote(outputfile)}'
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'the second tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    # 清理可能创建的pcap文件
                    if os.path.exists(outputfile):
                        try:
                            os.remove(outputfile)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup pcap file {outputfile}: {str(cleanup_e)}")
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None, None

            # 再次执行tcpprep命令，根据tcprewrite后输出的报文进行重新生成客户端和服务器区分的cache，避免报文分片后cache文件没有更新
            tcpprep = f'tcpprep -p -s {service} -i {shlex.quote(outputfile)} -o {shlex.quote(cache)}'
            try:
                r = subprocess.getstatusoutput(tcpprep)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    # 清理可能创建的pcap文件
                    if os.path.exists(outputfile):
                        try:
                            os.remove(outputfile)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup pcap file {outputfile}: {str(cleanup_e)}")
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcpprep command: {str(e)}")
                return None, None

            return cache, outputfile

        except Exception as e:
            self.logger.error(f"Error in pcapReplay.rewrite: {str(e)}")
            return None, None

    def replay(self, **kwargs):
        try:
            # 参数验证
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']
            interface1 = kwargs.get('interface1', '')
            interface2 = kwargs.get('interface2') if kwargs.get('interface2') else interface1

            # 验证网络接口参数
            if not interface1:
                self.logger.error("Network interface1 is required for packet replay")
                return False

            # 调用rewrite方法
            cache, pcapNew = self.rewrite(srcIp=srcIp, dstIp=dstIp, pcapPath=pcapPath)

            # 检查rewrite是否成功
            if cache is None or pcapNew is None:
                self.logger.error("Failed to rewrite pcap file, cannot proceed with replay")
                return False

            # 验证生成的文件是否存在
            if not os.path.exists(cache) or not os.path.exists(pcapNew):
                self.logger.error(f"Required files not found - cache: {cache}, pcapNew: {pcapNew}")
                return False

            # 执行tcpreplay命令
            tcpreplay = f'tcpreplay --loop=1 -c {shlex.quote(cache)} -i {interface1} -I {interface2} {shlex.quote(pcapNew)}'
            try:
                r = subprocess.getstatusoutput(tcpreplay)
                errorMsg=r[1].replace("\n", " ")
                if r[0] != 0:
                    
                    self.logger.error(f'tcpreplay failed with exit code {r[0]}, result: {errorMsg}')
                    return False
                else:
                    self.logger.info(f'Packet replay successful, result: {errorMsg}')
            except Exception as e:
                self.logger.error(f"Error executing tcpreplay command: {str(e)}")
                return False
            finally:

                # 清理临时文件
                cleanup_success = True
                if os.path.exists(cache):
                    try:
                        os.remove(cache)
                        self.logger.info(f"Deleted temporary cache file: {cache}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete cache file {cache}: {str(e)}")
                        cleanup_success = False

                if os.path.exists(pcapNew):
                    try:
                        os.remove(pcapNew)
                        self.logger.info(f"Deleted temporary pcap file: {pcapNew}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete pcap file {pcapNew}: {str(e)}")
                        cleanup_success = False

                return cleanup_success

        except Exception as e:
            self.logger.error(f"Error in pcapReplay.replay: {str(e)}")
            return False

    def _generate_ip_address(self, base_ip, increment):
        """
        生成IP地址，支持正确的自增长

        Args:
            base_ip (str): 基础IP地址，如 '*******'
            increment (int): 增长数值

        Returns:
            str: 生成的IP地址

        Examples:
            _generate_ip_address('*******', 0) -> '*******'
            _generate_ip_address('*******', 255) -> '*********'
            _generate_ip_address('*******', 256) -> '*******'
            _generate_ip_address('*******', 65536) -> '*******'
        """
        try:
            # 将IP地址转换为整数
            parts = base_ip.split('.')
            if len(parts) != 4:
                raise ValueError(f"Invalid IP address format: {base_ip}")

            # 验证每个部分都是有效的0-255范围
            for part in parts:
                if not part.isdigit() or not (0 <= int(part) <= 255):
                    raise ValueError(f"Invalid IP address part: {part}")

            # 将IP地址转换为32位整数
            ip_int = (int(parts[0]) << 24) + (int(parts[1]) << 16) + (int(parts[2]) << 8) + int(parts[3])

            # 加上增长值
            new_ip_int = ip_int + increment

            # 确保不超过32位整数的最大值（避免溢出）
            if new_ip_int > 0xFFFFFFFF:
                new_ip_int = 0xFFFFFFFF

            # 将整数转换回IP地址
            new_parts = [
                (new_ip_int >> 24) & 0xFF,
                (new_ip_int >> 16) & 0xFF,
                (new_ip_int >> 8) & 0xFF,
                new_ip_int & 0xFF
            ]

            return '.'.join(map(str, new_parts))

        except Exception as e:
            self.logger.error(f"Error generating IP address from {base_ip} with increment {increment}: {str(e)}")
            # 如果出错，返回基础IP地址
            return base_ip

    def _validate_pcap_format(self, pcap_path):
        """
        验证pcap文件格式的基本有效性
        """
        try:
            with open(pcap_path, 'rb') as f:
                # 读取文件头部
                header = f.read(24)

                if len(header) < 24:
                    self.logger.warning(f"Pcap file too small: {pcap_path}")
                    return False

                # 检查pcap文件魔数
                magic_numbers = [
                    b'\xd4\xc3\xb2\xa1',  # 标准pcap (little endian)
                    b'\xa1\xb2\xc3\xd4',  # 标准pcap (big endian)
                    b'\x0a\x0d\x0d\x0a',  # pcapng
                    b'\x4d\x3c\xb2\xa1',  # 修改的pcap
                    b'\xa1\xb2\x3c\x4d'   # 修改的pcap (big endian)
                ]

                file_magic = header[:4]
                if file_magic not in magic_numbers:
                    self.logger.warning(f"Invalid pcap magic number in file: {pcap_path}")
                    return False

                # 对于pcap文件，尝试读取第一个数据包头部
                if file_magic in [b'\xd4\xc3\xb2\xa1', b'\xa1\xb2\xc3\xd4']:
                    # 跳过全局头部，尝试读取第一个数据包记录头部
                    packet_header = f.read(16)
                    if len(packet_header) < 16:
                        self.logger.warning(f"No packet data found in pcap file: {pcap_path}")
                        return False

                    # 解析数据包长度
                    if file_magic == b'\xd4\xc3\xb2\xa1':  # little endian
                        import struct
                        _, _, caplen, _ = struct.unpack('<IIII', packet_header)
                    else:  # big endian
                        import struct
                        _, _, caplen, _ = struct.unpack('>IIII', packet_header)

                    # 检查数据包长度是否合理
                    if caplen == 0 or caplen > 65535:
                        self.logger.warning(f"Invalid packet length ({caplen}) in pcap file: {pcap_path}")
                        return False

                self.logger.info(f"Pcap file format validation passed: {pcap_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error validating pcap format for {pcap_path}: {str(e)}")
            return False
    def randomIpAddress(self):
        """使用ipaddress模块生成随机IPv4地址"""
        # 生成随机的32位整数
        random_int = random.randint(0, 2**32 - 1)
        # 转换为IPv4地址
        return str(ipaddress.IPv4Address(random_int))
