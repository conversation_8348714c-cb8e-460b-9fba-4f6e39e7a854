# -*- coding: utf-8 -*-
"""
HTTP负载模拟访问核心功能模块
"""

import os
import socket
import subprocess
import threading
import time
import signal
import requests
import re
from urllib.parse import urlparse
from typing import List, Dict, Any, Optional, Tuple
import logging


class HttpLoadSimulator:
    """HTTP负载模拟器"""
    
    def __init__(self, plugin_dir: str, logger: logging.Logger):
        self.pluginDir = plugin_dir
        self.logger = logger
        self.tcpdumpProcess = None
        self.tcpdumpLock = threading.Lock()

    def simulateHttpLoad(self, http_payload: str, target_ip: str, target_port: int, output_dir: str) -> Dict[str, Any]:
        """
        模拟HTTP负载访问并抓包

        Args:
            http_payload: HTTP负载内容（完整的HTTP请求）
            target_ip: 目标IP地址
            target_port: 目标端口
            output_dir: 输出pcap文件目录

        Returns:
            Dict: 执行结果
        """
        try:
            self.logger.info(f"开始HTTP负载模拟，目标: {target_ip}:{target_port}")

            # 解析HTTP负载
            parsed_request = self._parseHttpPayload(http_payload)
            if not parsed_request:
                return {
                    'success': False,
                    'message': 'HTTP负载解析失败，请检查格式是否正确'
                }

            # 创建抓包文件路径
            pcap_file = os.path.join(output_dir, f"http_load_{target_ip}_{target_port}.pcap")

            # 启动tcpdump抓包
            if not self._startTcpdump(target_port, pcap_file):
                return {
                    'success': False,
                    'message': 'tcpdump抓包启动失败'
                }

            # 等待tcpdump启动
            time.sleep(2)

            # 执行HTTP请求
            result = self._sendHttpRequest(parsed_request, target_ip, target_port)

            # 等待网络交互完成
            time.sleep(2)

            # 停止tcpdump
            self._stopTcpdump()

            # 检查抓包文件
            if os.path.exists(pcap_file) and os.path.getsize(pcap_file) > 0:
                return {
                    'success': True,
                    'message': f'HTTP负载模拟完成，成功发送请求到 {target_ip}:{target_port}，响应: {result.get("response", "无响应")}',
                    'details': {
                        'target_ip': target_ip,
                        'target_port': target_port,
                        'method': parsed_request.get('method', 'UNKNOWN'),
                        'path': parsed_request.get('path', '/'),
                        'response_status': result.get('status_code', 'N/A'),
                        'pcap_file': pcap_file
                    }
                }
            else:
                return {
                    'success': False,
                    'message': '抓包文件生成失败或为空'
                }

        except Exception as e:
            self.logger.error(f"HTTP负载模拟失败: {str(e)}")
            self._stopTcpdump()
            return {
                'success': False,
                'message': f'HTTP负载模拟失败: {str(e)}'
            }

    def _parseHttpPayload(self, http_payload: str) -> Optional[Dict[str, Any]]:
        """
        解析HTTP负载内容

        Args:
            http_payload: HTTP负载字符串

        Returns:
            Dict: 解析后的HTTP请求信息
        """
        try:
            lines = http_payload.strip().split('\n')
            if not lines:
                return None

            # 解析请求行
            request_line = lines[0].strip()
            parts = request_line.split(' ')
            if len(parts) < 3:
                return None

            method = parts[0]
            path = parts[1]
            version = parts[2]

            # 解析请求头
            headers = {}
            body_start = 1
            for i, line in enumerate(lines[1:], 1):
                line = line.strip()
                if not line:  # 空行表示头部结束
                    body_start = i + 1
                    break
                
                if ':' in line:
                    key, value = line.split(':', 1)
                    headers[key.strip()] = value.strip()

            # 解析请求体
            body = ''
            if body_start < len(lines):
                body = '\n'.join(lines[body_start:]).strip()

            return {
                'method': method,
                'path': path,
                'version': version,
                'headers': headers,
                'body': body
            }

        except Exception as e:
            self.logger.error(f"解析HTTP负载失败: {str(e)}")
            return None

    def _sendHttpRequest(self, parsed_request: Dict[str, Any], target_ip: str, target_port: int) -> Dict[str, Any]:
        """
        发送HTTP请求

        Args:
            parsed_request: 解析后的HTTP请求
            target_ip: 目标IP地址
            target_port: 目标端口

        Returns:
            Dict: 请求结果
        """
        try:
            method = parsed_request.get('method', 'GET')
            path = parsed_request.get('path', '/')
            headers = parsed_request.get('headers', {})
            body = parsed_request.get('body', '')

            # 构建URL
            url = f"http://{target_ip}:{target_port}{path}"

            # 准备请求参数
            request_kwargs = {
                'headers': headers,
                'timeout': 10,
                'allow_redirects': False
            }

            # 如果有请求体，添加到请求中
            if body:
                if method.upper() in ['POST', 'PUT', 'PATCH']:
                    # 根据Content-Type处理请求体
                    content_type = headers.get('Content-Type', '').lower()
                    if 'application/json' in content_type:
                        request_kwargs['json'] = body
                    elif 'application/x-www-form-urlencoded' in content_type:
                        request_kwargs['data'] = body
                    else:
                        request_kwargs['data'] = body

            # 发送请求
            response = requests.request(method, url, **request_kwargs)

            return {
                'success': True,
                'status_code': response.status_code,
                'response': f"HTTP {response.status_code} {response.reason}",
                'headers': dict(response.headers),
                'content_length': len(response.content)
            }

        except requests.exceptions.RequestException as e:
            # 即使请求失败，也算成功，因为生成了网络流量
            return {
                'success': True,
                'status_code': 'N/A',
                'response': f"网络交互完成（连接异常: {str(e)}）",
                'note': '模拟HTTP负载交互'
            }
        except Exception as e:
            self.logger.error(f"发送HTTP请求失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _startTcpdump(self, port: int, output_file: str) -> bool:
        """启动tcpdump抓包"""
        try:
            with self.tcpdumpLock:
                # 停止之前的tcpdump进程
                self._stopTcpdump()
                
                # 构建tcpdump命令
                cmd = [
                    'tcpdump',
                    '-i', 'any',
                    '-w', output_file,
                    f'port {port}'
                ]
                
                # 启动tcpdump进程
                self.tcpdumpProcess = subprocess.Popen(
                    cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    preexec_fn=os.setsid
                )
                
                self.logger.info(f"tcpdump已启动，PID: {self.tcpdumpProcess.pid}")
                return True
                
        except Exception as e:
            self.logger.error(f"启动tcpdump失败: {str(e)}")
            return False

    def _stopTcpdump(self):
        """停止tcpdump抓包"""
        try:
            with self.tcpdumpLock:
                if self.tcpdumpProcess:
                    # 发送SIGTERM信号终止进程组
                    os.killpg(os.getpgid(self.tcpdumpProcess.pid), signal.SIGTERM)
                    
                    # 等待进程结束
                    try:
                        self.tcpdumpProcess.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        # 如果进程没有在5秒内结束，强制杀死
                        os.killpg(os.getpgid(self.tcpdumpProcess.pid), signal.SIGKILL)
                        self.tcpdumpProcess.wait()
                    
                    self.logger.info("tcpdump已停止")
                    self.tcpdumpProcess = None
                    
        except Exception as e:
            self.logger.error(f"停止tcpdump失败: {str(e)}")
            self.tcpdumpProcess = None
