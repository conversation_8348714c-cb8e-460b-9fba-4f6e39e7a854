2025-07-22 17:36:27 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:36:27 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:36:27 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:36:28 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:36:28 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:36:28 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:43:04 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:43:04 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:43:04 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:43:37 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:43:37 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:43:37 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:43:38 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:43:38 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:43:38 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:45:28 - plugin.httpLoadSimulator - INFO - 开始处理批量任务创建请求
2025-07-22 17:45:28 - plugin.httpLoadSimulator - INFO - 接收到参数: taskName=test1, targetIp=12*******, targetPort=8088, csvFile=test.csv
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - 开始执行批量任务: 494ba107-487a-40ee-92fd-4eda3dd6b774，共17个测试项目
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - 执行测试项目 1/17: Fastjson_代码执行_漏洞验证
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:45:29 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/Fastjson_代码执行_漏洞验证.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 17:45:31 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/web/admin/sysDictionaryInfo/getListByDictionaryName completed with status code 200
2025-07-22 17:45:33 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:45:35 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/Fastjson_代码执行_漏洞验证.pcap
2025-07-22 17:45:35 - plugin.httpLoadSimulator - INFO - 测试项目 'Fastjson_代码执行_漏洞验证' 执行成功
2025-07-22 17:45:35 - plugin.httpLoadSimulator - INFO - 执行测试项目 2/17: 东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw
2025-07-22 17:45:35 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:45:35 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 17:45:35 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:45:35 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_remotecall_远程代码执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 17:45:37 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/console/service completed with status code 200
2025-07-22 17:45:39 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:45:42 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_remotecall_远程代码执行漏洞-2-2023hw.pcap
2025-07-22 17:45:42 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw' 执行成功
2025-07-22 17:45:42 - plugin.httpLoadSimulator - INFO - 执行测试项目 3/17: 东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw
2025-07-22 17:45:42 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:45:42 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 17:45:42 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:45:42 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_remotecall_远程代码执行漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 17:45:44 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 17:45:46 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:45:48 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_remotecall_远程代码执行漏洞-1-2023hw.pcap
2025-07-22 17:45:48 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw' 执行成功
2025-07-22 17:45:48 - plugin.httpLoadSimulator - INFO - 执行测试项目 4/17: 东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw
2025-07-22 17:45:48 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:45:48 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8091
2025-07-22 17:45:48 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:45:48 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_后台存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8091
2025-07-22 17:45:50 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8091/console/css/5f8f4ab7.jsp completed with status code 200
2025-07-22 17:45:53 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:45:55 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 17:45:55 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 17:45:55 - plugin.httpLoadSimulator - INFO - 执行测试项目 5/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw
2025-07-22 17:45:55 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:45:55 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8092
2025-07-22 17:45:55 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:45:55 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_deployupload_接口存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8092
2025-07-22 17:45:57 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8092/console/css/abcd.jsp completed with status code 200
2025-07-22 17:45:59 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:02 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_deployupload_接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 17:46:02 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 17:46:02 - plugin.httpLoadSimulator - INFO - 执行测试项目 6/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw
2025-07-22 17:46:02 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:02 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8093
2025-07-22 17:46:02 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:02 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_deployupload_接口存在任意文件上传漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8093
2025-07-22 17:46:04 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8093/heimdall/deploy/upload?method=upload completed with status code 200
2025-07-22 17:46:06 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:08 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_deployupload_接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 17:46:08 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw' 执行成功
2025-07-22 17:46:08 - plugin.httpLoadSimulator - INFO - 执行测试项目 7/17: 东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw
2025-07-22 17:46:08 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:08 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8094
2025-07-22 17:46:08 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:08 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_selectAppjsp_路径存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8094
2025-07-22 17:46:10 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8094/heimdall/abcd.jsp completed with status code 200
2025-07-22 17:46:12 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:15 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_selectAppjsp_路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 17:46:15 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 17:46:15 - plugin.httpLoadSimulator - INFO - 执行测试项目 8/17: 契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw
2025-07-22 17:46:15 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:15 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8095
2025-07-22 17:46:15 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:15 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/契约锁-电子签章系统_codeupload_接口存在任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8095
2025-07-22 17:46:17 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8095/callback/%2E%2E;/code/download?codeId=12345 completed with status code 200
2025-07-22 17:46:19 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:21 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/契约锁-电子签章系统_codeupload_接口存在任意文件上传-2-2023hw.pcap
2025-07-22 17:46:21 - plugin.httpLoadSimulator - INFO - 测试项目 '契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw' 执行成功
2025-07-22 17:46:21 - plugin.httpLoadSimulator - INFO - 执行测试项目 9/17: Milesight VPN 路径穿越漏洞-2023hw
2025-07-22 17:46:21 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:21 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8096
2025-07-22 17:46:21 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:21 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/Milesight_VPN_路径穿越漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8096
2025-07-22 17:46:23 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8096/../etc/passwd completed with status code 200
2025-07-22 17:46:26 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:28 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/Milesight_VPN_路径穿越漏洞-2023hw.pcap
2025-07-22 17:46:28 - plugin.httpLoadSimulator - INFO - 测试项目 'Milesight VPN 路径穿越漏洞-2023hw' 执行成功
2025-07-22 17:46:28 - plugin.httpLoadSimulator - INFO - 执行测试项目 10/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw
2025-07-22 17:46:28 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:28 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8097
2025-07-22 17:46:28 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:28 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/用友时空KSOA软件_TaskRequestServlet_SQL注入漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8097
2025-07-22 17:46:30 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8097/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=4723--&password=1 completed with status code 200
2025-07-22 17:46:32 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:35 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/用友时空KSOA软件_TaskRequestServlet_SQL注入漏洞-2-2023hw.pcap
2025-07-22 17:46:35 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw' 执行成功
2025-07-22 17:46:35 - plugin.httpLoadSimulator - INFO - 执行测试项目 11/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw
2025-07-22 17:46:35 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:35 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 17:46:35 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:35 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/用友时空KSOA软件_TaskRequestServlet_SQL注入漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 17:46:37 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=78451--&password=1 completed with status code 200
2025-07-22 17:46:39 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:41 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/用友时空KSOA软件_TaskRequestServlet_SQL注入漏洞-1-2023hw.pcap
2025-07-22 17:46:41 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw' 执行成功
2025-07-22 17:46:41 - plugin.httpLoadSimulator - INFO - 执行测试项目 12/17: 锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw
2025-07-22 17:46:41 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:41 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8098
2025-07-22 17:46:41 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:41 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/锐捷EG网关_ddiserverfileuploadphp_文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8098
2025-07-22 17:46:43 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8098/ijklmnop.php completed with status code 200
2025-07-22 17:46:45 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:48 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/锐捷EG网关_ddiserverfileuploadphp_文件上传漏洞-2-2023hw.pcap
2025-07-22 17:46:48 - plugin.httpLoadSimulator - INFO - 测试项目 '锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw' 执行成功
2025-07-22 17:46:48 - plugin.httpLoadSimulator - INFO - 执行测试项目 13/17: 海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw
2025-07-22 17:46:48 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:48 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 17:46:48 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:48 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/海康威视-综合安防管理平台_centerapifiles_任意文件上传漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 17:46:50 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/center/api/files;.jsp completed with status code 200
2025-07-22 17:46:52 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:46:54 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/海康威视-综合安防管理平台_centerapifiles_任意文件上传漏洞-2023hw.pcap
2025-07-22 17:46:54 - plugin.httpLoadSimulator - INFO - 测试项目 '海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw' 执行成功
2025-07-22 17:46:54 - plugin.httpLoadSimulator - INFO - 执行测试项目 14/17: 通达OA /module/upload/upload.php 任意文件上传-2-2023hw
2025-07-22 17:46:54 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:46:54 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 17:46:54 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:46:54 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/通达OA_moduleuploaduploadphp_任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 17:46:56 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/module/upload/upload.php?module=im completed with status code 200
2025-07-22 17:46:58 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:47:01 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/通达OA_moduleuploaduploadphp_任意文件上传-2-2023hw.pcap
2025-07-22 17:47:01 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-2-2023hw' 执行成功
2025-07-22 17:47:01 - plugin.httpLoadSimulator - INFO - 执行测试项目 15/17: 通达OA /module/upload/upload.php 任意文件上传-1-2023hw
2025-07-22 17:47:01 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:47:01 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8091
2025-07-22 17:47:01 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:47:01 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/通达OA_moduleuploaduploadphp_任意文件上传-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8091
2025-07-22 17:47:03 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8091/general/system/attachment/position/add.php completed with status code 200
2025-07-22 17:47:05 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:47:08 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/通达OA_moduleuploaduploadphp_任意文件上传-1-2023hw.pcap
2025-07-22 17:47:08 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-1-2023hw' 执行成功
2025-07-22 17:47:08 - plugin.httpLoadSimulator - INFO - 执行测试项目 16/17: 深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw
2025-07-22 17:47:08 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:47:08 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8092
2025-07-22 17:47:08 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:47:08 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/深信服应用交付系统_replogin_远程命令执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8092
2025-07-22 17:47:10 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8092/153287 completed with status code 200
2025-07-22 17:47:12 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:47:14 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/深信服应用交付系统_replogin_远程命令执行漏洞-2-2023hw.pcap
2025-07-22 17:47:14 - plugin.httpLoadSimulator - INFO - 测试项目 '深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw' 执行成功
2025-07-22 17:47:14 - plugin.httpLoadSimulator - INFO - 执行测试项目 17/17: 东方通TongWeb 安装路径敏感信息泄露-2023hw
2025-07-22 17:47:14 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 17:47:14 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8093
2025-07-22 17:47:14 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 17:47:14 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_安装路径敏感信息泄露-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8093
2025-07-22 17:47:16 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8093/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 17:47:18 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 17:47:21 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/东方通TongWeb_安装路径敏感信息泄露-2023hw.pcap
2025-07-22 17:47:21 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 安装路径敏感信息泄露-2023hw' 执行成功
2025-07-22 17:47:21 - plugin.httpLoadSimulator - INFO - 成功将 17 条结果写入CSV文件: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/494ba107-487a-40ee-92fd-4eda3dd6b774_results.csv
2025-07-22 17:47:21 - plugin.httpLoadSimulator - INFO - 批量任务 494ba107-487a-40ee-92fd-4eda3dd6b774 执行完成，成功: 17个，失败: 0个
2025-07-22 17:47:21 - plugin.httpLoadSimulator - INFO - 结果已保存到: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/494ba107-487a-40ee-92fd-4eda3dd6b774/494ba107-487a-40ee-92fd-4eda3dd6b774_results.csv
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb_remotecall_远程代码执行漏洞-1-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 海康威视-综合安防管理平台_centerapifiles_任意文件上传漏洞-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 494ba107-487a-40ee-92fd-4eda3dd6b774_results.csv
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加测试结果文件到压缩包: 494ba107-487a-40ee-92fd-4eda3dd6b774_results.csv
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb_安装路径敏感信息泄露-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 契约锁-电子签章系统_codeupload_接口存在任意文件上传-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb_remotecall_远程代码执行漏洞-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: Fastjson_代码执行_漏洞验证.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb_deployupload_接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: test1.csv
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加测试结果文件到压缩包: test1.csv
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 用友时空KSOA软件_TaskRequestServlet_SQL注入漏洞-1-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 用友时空KSOA软件_TaskRequestServlet_SQL注入漏洞-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 通达OA_moduleuploaduploadphp_任意文件上传-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb_deployupload_接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 通达OA_moduleuploaduploadphp_任意文件上传-1-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: Milesight_VPN_路径穿越漏洞-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb_selectAppjsp_路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 深信服应用交付系统_replogin_远程命令执行漏洞-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb_后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 锐捷EG网关_ddiserverfileuploadphp_文件上传漏洞-2-2023hw.pcap
2025-07-22 17:47:41 - plugin.httpLoadSimulator - INFO - 成功创建任务报文压缩包: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/http_load_report_494ba107-487a-40ee-92fd-4eda3dd6b774.zip，包含 19 个pcap文件
