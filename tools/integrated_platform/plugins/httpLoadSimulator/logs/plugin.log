2025-07-22 15:05:21 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:05:21 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:05:21 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:05:31 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:05:31 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:05:31 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:05:32 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:05:32 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:05:32 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 开始处理批量任务创建请求
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 接收到参数: taskName=test1, targetIp=12*******, targetPort=8088, csvFile=test.csv
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 开始执行批量任务: ac6716fc-e16d-4126-95ed-5e281423e8cc，共17个测试项目
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 执行测试项目 1/17: Fastjson_代码执行_漏洞验证
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:00 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/Fastjson_代码执行_漏洞验证.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 15:06:02 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/web/admin/sysDictionaryInfo/getListByDictionaryName completed with status code 200
2025-07-22 15:06:04 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:05 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/Fastjson_代码执行_漏洞验证.pcap
2025-07-22 15:06:05 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:05 - plugin.httpLoadSimulator - INFO - 测试项目 'Fastjson_代码执行_漏洞验证' 执行成功
2025-07-22 15:06:05 - plugin.httpLoadSimulator - INFO - 执行测试项目 2/17: 东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw
2025-07-22 15:06:05 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:05 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 15:06:05 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:05 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 15:06:07 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/console/service completed with status code 200
2025-07-22 15:06:09 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:10 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap
2025-07-22 15:06:10 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:10 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw' 执行成功
2025-07-22 15:06:10 - plugin.httpLoadSimulator - INFO - 执行测试项目 3/17: 东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw
2025-07-22 15:06:10 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:10 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 15:06:10 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:10 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 15:06:12 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 15:06:14 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:14 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap
2025-07-22 15:06:14 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:14 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw' 执行成功
2025-07-22 15:06:14 - plugin.httpLoadSimulator - INFO - 执行测试项目 4/17: 东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw
2025-07-22 15:06:14 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:14 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8091
2025-07-22 15:06:14 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:14 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8091
2025-07-22 15:06:16 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8091/console/css/5f8f4ab7.jsp completed with status code 200
2025-07-22 15:06:18 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:19 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:06:19 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:19 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:06:19 - plugin.httpLoadSimulator - INFO - 执行测试项目 5/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw
2025-07-22 15:06:19 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:19 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8092
2025-07-22 15:06:19 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:19 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8092
2025-07-22 15:06:21 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8092/console/css/abcd.jsp completed with status code 200
2025-07-22 15:06:23 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:23 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:06:23 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:23 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:06:23 - plugin.httpLoadSimulator - INFO - 执行测试项目 6/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw
2025-07-22 15:06:23 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:23 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8093
2025-07-22 15:06:23 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:23 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8093
2025-07-22 15:06:25 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8093/heimdall/deploy/upload?method=upload completed with status code 200
2025-07-22 15:06:27 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:28 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 15:06:28 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:28 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw' 执行成功
2025-07-22 15:06:28 - plugin.httpLoadSimulator - INFO - 执行测试项目 7/17: 东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw
2025-07-22 15:06:28 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:28 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8094
2025-07-22 15:06:28 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:28 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8094
2025-07-22 15:06:30 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8094/heimdall/abcd.jsp completed with status code 200
2025-07-22 15:06:32 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:32 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:06:32 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:32 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:06:32 - plugin.httpLoadSimulator - INFO - 执行测试项目 8/17: 契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw
2025-07-22 15:06:32 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:32 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8095
2025-07-22 15:06:32 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:32 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8095
2025-07-22 15:06:34 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8095/callback/%2E%2E;/code/download?codeId=12345 completed with status code 200
2025-07-22 15:06:37 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:37 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap
2025-07-22 15:06:37 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:37 - plugin.httpLoadSimulator - INFO - 测试项目 '契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw' 执行成功
2025-07-22 15:06:37 - plugin.httpLoadSimulator - INFO - 执行测试项目 9/17: Milesight VPN 路径穿越漏洞-2023hw
2025-07-22 15:06:37 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:37 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8096
2025-07-22 15:06:37 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:37 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/Milesight VPN 路径穿越漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8096
2025-07-22 15:06:39 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8096/../etc/passwd completed with status code 200
2025-07-22 15:06:41 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:42 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/Milesight VPN 路径穿越漏洞-2023hw.pcap
2025-07-22 15:06:42 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:42 - plugin.httpLoadSimulator - INFO - 测试项目 'Milesight VPN 路径穿越漏洞-2023hw' 执行成功
2025-07-22 15:06:42 - plugin.httpLoadSimulator - INFO - 执行测试项目 10/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw
2025-07-22 15:06:42 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:42 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8097
2025-07-22 15:06:42 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:42 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8097
2025-07-22 15:06:44 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8097/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=4723--&password=1 completed with status code 200
2025-07-22 15:06:46 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:46 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap
2025-07-22 15:06:46 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:46 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw' 执行成功
2025-07-22 15:06:46 - plugin.httpLoadSimulator - INFO - 执行测试项目 11/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw
2025-07-22 15:06:46 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:46 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8098
2025-07-22 15:06:46 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:46 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8098
2025-07-22 15:06:48 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8098/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=78451--&password=1 completed with status code 200
2025-07-22 15:06:50 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:51 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap
2025-07-22 15:06:51 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:51 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw' 执行成功
2025-07-22 15:06:51 - plugin.httpLoadSimulator - INFO - 执行测试项目 12/17: 锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw
2025-07-22 15:06:51 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:51 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8099
2025-07-22 15:06:51 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:51 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8099
2025-07-22 15:06:53 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8099/ijklmnop.php completed with status code 200
2025-07-22 15:06:55 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:06:55 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap
2025-07-22 15:06:55 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:06:55 - plugin.httpLoadSimulator - INFO - 测试项目 '锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:06:55 - plugin.httpLoadSimulator - INFO - 执行测试项目 13/17: 海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw
2025-07-22 15:06:55 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:06:55 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8100
2025-07-22 15:06:55 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:06:55 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8100
2025-07-22 15:06:57 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8100/center/api/files;.jsp completed with status code 200
2025-07-22 15:06:59 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:07:00 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap
2025-07-22 15:07:00 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:07:00 - plugin.httpLoadSimulator - INFO - 测试项目 '海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw' 执行成功
2025-07-22 15:07:00 - plugin.httpLoadSimulator - INFO - 执行测试项目 14/17: 通达OA /module/upload/upload.php 任意文件上传-2-2023hw
2025-07-22 15:07:00 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:07:00 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8101
2025-07-22 15:07:00 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:07:00 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8101
2025-07-22 15:07:02 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8101/module/upload/upload.php?module=im completed with status code 200
2025-07-22 15:07:04 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:07:04 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap
2025-07-22 15:07:05 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:07:05 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-2-2023hw' 执行成功
2025-07-22 15:07:05 - plugin.httpLoadSimulator - INFO - 执行测试项目 15/17: 通达OA /module/upload/upload.php 任意文件上传-1-2023hw
2025-07-22 15:07:05 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:07:05 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8102
2025-07-22 15:07:05 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:07:05 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8102
2025-07-22 15:07:07 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8102/general/system/attachment/position/add.php completed with status code 200
2025-07-22 15:07:09 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:07:09 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap
2025-07-22 15:07:09 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:07:09 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-1-2023hw' 执行成功
2025-07-22 15:07:09 - plugin.httpLoadSimulator - INFO - 执行测试项目 16/17: 深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw
2025-07-22 15:07:09 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:07:09 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 15:07:09 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:07:09 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 15:07:11 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/153287 completed with status code 200
2025-07-22 15:07:13 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:07:14 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap
2025-07-22 15:07:14 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:07:14 - plugin.httpLoadSimulator - INFO - 测试项目 '深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw' 执行成功
2025-07-22 15:07:14 - plugin.httpLoadSimulator - INFO - 执行测试项目 17/17: 东方通TongWeb 安装路径敏感信息泄露-2023hw
2025-07-22 15:07:14 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:07:14 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 15:07:14 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:07:14 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 15:07:16 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 15:07:18 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:07:18 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ac6716fc-e16d-4126-95ed-5e281423e8cc/东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap
2025-07-22 15:07:18 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:07:18 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 安装路径敏感信息泄露-2023hw' 执行成功
2025-07-22 15:07:18 - plugin.httpLoadSimulator - INFO - 批量任务 ac6716fc-e16d-4126-95ed-5e281423e8cc 执行完成，成功: 17个，失败: 0个
2025-07-22 15:10:01 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:10:01 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:10:01 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:13:14 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:13:14 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:13:14 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:13:29 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:13:29 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:13:29 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:13:30 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:13:30 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:13:30 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 开始处理批量任务创建请求
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 接收到参数: taskName=test111, targetIp=12*******, targetPort=8088, csvFile=test.csv
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 开始执行批量任务: 33ebc8c6-2669-4f76-8367-98f585cf8e11，共17个测试项目
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 执行测试项目 1/17: Fastjson_代码执行_漏洞验证
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:13:52 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/Fastjson_代码执行_漏洞验证.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 15:13:54 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/web/admin/sysDictionaryInfo/getListByDictionaryName completed with status code 200
2025-07-22 15:13:56 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:13:56 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/Fastjson_代码执行_漏洞验证.pcap
2025-07-22 15:13:56 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:13:56 - plugin.httpLoadSimulator - INFO - 测试项目 'Fastjson_代码执行_漏洞验证' 执行成功
2025-07-22 15:13:56 - plugin.httpLoadSimulator - INFO - 执行测试项目 2/17: 东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw
2025-07-22 15:13:56 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:13:56 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 15:13:56 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:13:56 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 15:13:58 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/console/service completed with status code 200
2025-07-22 15:14:01 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:01 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap
2025-07-22 15:14:01 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:01 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw' 执行成功
2025-07-22 15:14:01 - plugin.httpLoadSimulator - INFO - 执行测试项目 3/17: 东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw
2025-07-22 15:14:01 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:01 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 15:14:01 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:01 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 15:14:03 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 15:14:05 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:06 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap
2025-07-22 15:14:06 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:06 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw' 执行成功
2025-07-22 15:14:06 - plugin.httpLoadSimulator - INFO - 执行测试项目 4/17: 东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw
2025-07-22 15:14:06 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:06 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8091
2025-07-22 15:14:06 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:06 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8091
2025-07-22 15:14:08 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8091/console/css/5f8f4ab7.jsp completed with status code 200
2025-07-22 15:14:10 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:10 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:14:10 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:10 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:14:10 - plugin.httpLoadSimulator - INFO - 执行测试项目 5/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw
2025-07-22 15:14:10 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:10 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8092
2025-07-22 15:14:10 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:10 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8092
2025-07-22 15:14:12 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8092/console/css/abcd.jsp completed with status code 200
2025-07-22 15:14:14 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:15 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:14:15 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:15 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:14:15 - plugin.httpLoadSimulator - INFO - 执行测试项目 6/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw
2025-07-22 15:14:15 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:15 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8093
2025-07-22 15:14:15 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:15 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8093
2025-07-22 15:14:17 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8093/heimdall/deploy/upload?method=upload completed with status code 200
2025-07-22 15:14:19 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:19 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 15:14:19 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:19 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw' 执行成功
2025-07-22 15:14:19 - plugin.httpLoadSimulator - INFO - 执行测试项目 7/17: 东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw
2025-07-22 15:14:19 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:19 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8094
2025-07-22 15:14:19 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:19 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8094
2025-07-22 15:14:21 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8094/heimdall/abcd.jsp completed with status code 200
2025-07-22 15:14:23 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:24 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:14:24 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:24 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:14:24 - plugin.httpLoadSimulator - INFO - 执行测试项目 8/17: 契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw
2025-07-22 15:14:24 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:24 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8095
2025-07-22 15:14:24 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:24 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8095
2025-07-22 15:14:26 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8095/callback/%2E%2E;/code/download?codeId=12345 completed with status code 200
2025-07-22 15:14:28 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:28 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap
2025-07-22 15:14:29 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:29 - plugin.httpLoadSimulator - INFO - 测试项目 '契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw' 执行成功
2025-07-22 15:14:29 - plugin.httpLoadSimulator - INFO - 执行测试项目 9/17: Milesight VPN 路径穿越漏洞-2023hw
2025-07-22 15:14:29 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:29 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8096
2025-07-22 15:14:29 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:29 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/Milesight VPN 路径穿越漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8096
2025-07-22 15:14:31 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8096/../etc/passwd completed with status code 200
2025-07-22 15:14:33 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:33 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/Milesight VPN 路径穿越漏洞-2023hw.pcap
2025-07-22 15:14:33 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:33 - plugin.httpLoadSimulator - INFO - 测试项目 'Milesight VPN 路径穿越漏洞-2023hw' 执行成功
2025-07-22 15:14:33 - plugin.httpLoadSimulator - INFO - 执行测试项目 10/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw
2025-07-22 15:14:33 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:33 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8097
2025-07-22 15:14:33 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:33 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8097
2025-07-22 15:14:35 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8097/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=4723--&password=1 completed with status code 200
2025-07-22 15:14:37 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:38 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap
2025-07-22 15:14:38 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:38 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw' 执行成功
2025-07-22 15:14:38 - plugin.httpLoadSimulator - INFO - 执行测试项目 11/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw
2025-07-22 15:14:38 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:38 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8098
2025-07-22 15:14:38 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:38 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8098
2025-07-22 15:14:40 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8098/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=78451--&password=1 completed with status code 200
2025-07-22 15:14:42 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:42 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap
2025-07-22 15:14:42 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:42 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw' 执行成功
2025-07-22 15:14:42 - plugin.httpLoadSimulator - INFO - 执行测试项目 12/17: 锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw
2025-07-22 15:14:42 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:42 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8099
2025-07-22 15:14:42 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:42 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8099
2025-07-22 15:14:44 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8099/ijklmnop.php completed with status code 200
2025-07-22 15:14:46 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:47 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap
2025-07-22 15:14:47 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:47 - plugin.httpLoadSimulator - INFO - 测试项目 '锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:14:47 - plugin.httpLoadSimulator - INFO - 执行测试项目 13/17: 海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw
2025-07-22 15:14:47 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:47 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8100
2025-07-22 15:14:47 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:47 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8100
2025-07-22 15:14:49 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8100/center/api/files;.jsp completed with status code 200
2025-07-22 15:14:51 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:51 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap
2025-07-22 15:14:51 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:51 - plugin.httpLoadSimulator - INFO - 测试项目 '海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw' 执行成功
2025-07-22 15:14:51 - plugin.httpLoadSimulator - INFO - 执行测试项目 14/17: 通达OA /module/upload/upload.php 任意文件上传-2-2023hw
2025-07-22 15:14:51 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:51 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8101
2025-07-22 15:14:51 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:51 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8101
2025-07-22 15:14:53 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8101/module/upload/upload.php?module=im completed with status code 200
2025-07-22 15:14:55 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:14:56 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap
2025-07-22 15:14:56 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:14:56 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-2-2023hw' 执行成功
2025-07-22 15:14:56 - plugin.httpLoadSimulator - INFO - 执行测试项目 15/17: 通达OA /module/upload/upload.php 任意文件上传-1-2023hw
2025-07-22 15:14:56 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:14:56 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 15:14:56 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:14:56 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 15:14:58 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/general/system/attachment/position/add.php completed with status code 200
2025-07-22 15:15:00 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:15:01 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap
2025-07-22 15:15:01 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:15:01 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-1-2023hw' 执行成功
2025-07-22 15:15:01 - plugin.httpLoadSimulator - INFO - 执行测试项目 16/17: 深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw
2025-07-22 15:15:01 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:15:01 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 15:15:01 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:15:01 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 15:15:03 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/153287 completed with status code 200
2025-07-22 15:15:05 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:15:05 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap
2025-07-22 15:15:05 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:15:05 - plugin.httpLoadSimulator - INFO - 测试项目 '深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw' 执行成功
2025-07-22 15:15:05 - plugin.httpLoadSimulator - INFO - 执行测试项目 17/17: 东方通TongWeb 安装路径敏感信息泄露-2023hw
2025-07-22 15:15:05 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:15:05 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 15:15:05 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:15:05 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 15:15:07 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 15:15:09 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:15:10 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/33ebc8c6-2669-4f76-8367-98f585cf8e11/东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap
2025-07-22 15:15:10 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:15:10 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 安装路径敏感信息泄露-2023hw' 执行成功
2025-07-22 15:15:10 - plugin.httpLoadSimulator - INFO - 批量任务 33ebc8c6-2669-4f76-8367-98f585cf8e11 执行完成，成功: 17个，失败: 0个
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: Fastjson_代码执行_漏洞验证.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: Milesight VPN 路径穿越漏洞-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:19:31 - plugin.httpLoadSimulator - INFO - 成功创建任务报文压缩包: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/http_load_report_33ebc8c6-2669-4f76-8367-98f585cf8e11.zip，包含 17 个pcap文件
2025-07-22 15:20:50 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:20:50 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:20:50 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:21:01 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:21:01 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:21:01 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:21:02 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 15:21:02 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 15:21:02 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 15:21:29 - plugin.httpLoadSimulator - INFO - 开始处理批量任务创建请求
2025-07-22 15:21:29 - plugin.httpLoadSimulator - INFO - 接收到参数: taskName=test5, targetIp=12*******, targetPort=8088, csvFile=test.csv
2025-07-22 15:21:29 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 15:21:29 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 15:21:29 - plugin.httpLoadSimulator - INFO - 开始执行批量任务: afc81b7d-de20-450b-a2f6-ddb6d40bf372，共17个测试项目
2025-07-22 15:21:30 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 15:21:30 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 15:21:30 - plugin.httpLoadSimulator - INFO - 执行测试项目 1/17: Fastjson_代码执行_漏洞验证
2025-07-22 15:21:30 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:21:30 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 15:21:30 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:21:30 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/Fastjson_代码执行_漏洞验证.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 15:21:32 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/web/admin/sysDictionaryInfo/getListByDictionaryName completed with status code 200
2025-07-22 15:21:34 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:21:35 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/Fastjson_代码执行_漏洞验证.pcap
2025-07-22 15:21:35 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:21:35 - plugin.httpLoadSimulator - INFO - 测试项目 'Fastjson_代码执行_漏洞验证' 执行成功
2025-07-22 15:21:35 - plugin.httpLoadSimulator - INFO - 执行测试项目 2/17: 东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw
2025-07-22 15:21:35 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:21:35 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 15:21:35 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:21:35 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 15:21:37 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/console/service completed with status code 200
2025-07-22 15:21:39 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:21:41 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap
2025-07-22 15:21:41 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:21:41 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw' 执行成功
2025-07-22 15:21:41 - plugin.httpLoadSimulator - INFO - 执行测试项目 3/17: 东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw
2025-07-22 15:21:41 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:21:41 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 15:21:41 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:21:41 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 15:21:43 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 15:21:45 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:21:46 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap
2025-07-22 15:21:46 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:21:46 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw' 执行成功
2025-07-22 15:21:46 - plugin.httpLoadSimulator - INFO - 执行测试项目 4/17: 东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw
2025-07-22 15:21:46 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:21:46 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8091
2025-07-22 15:21:46 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:21:46 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8091
2025-07-22 15:21:48 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8091/console/css/5f8f4ab7.jsp completed with status code 200
2025-07-22 15:21:50 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:21:52 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:21:52 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:21:52 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:21:52 - plugin.httpLoadSimulator - INFO - 执行测试项目 5/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw
2025-07-22 15:21:52 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:21:52 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8092
2025-07-22 15:21:52 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:21:52 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8092
2025-07-22 15:21:54 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8092/console/css/abcd.jsp completed with status code 200
2025-07-22 15:21:56 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:21:57 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:21:58 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:21:58 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:21:58 - plugin.httpLoadSimulator - INFO - 执行测试项目 6/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw
2025-07-22 15:21:58 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:21:58 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8093
2025-07-22 15:21:58 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:21:58 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8093
2025-07-22 15:22:00 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8093/heimdall/deploy/upload?method=upload completed with status code 200
2025-07-22 15:22:02 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:03 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 15:22:03 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:03 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw' 执行成功
2025-07-22 15:22:03 - plugin.httpLoadSimulator - INFO - 执行测试项目 7/17: 东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw
2025-07-22 15:22:03 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:03 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8094
2025-07-22 15:22:03 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:03 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8094
2025-07-22 15:22:05 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8094/heimdall/abcd.jsp completed with status code 200
2025-07-22 15:22:07 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:09 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:22:09 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:09 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:22:09 - plugin.httpLoadSimulator - INFO - 执行测试项目 8/17: 契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw
2025-07-22 15:22:09 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:09 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8095
2025-07-22 15:22:09 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:09 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8095
2025-07-22 15:22:11 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8095/callback/%2E%2E;/code/download?codeId=12345 completed with status code 200
2025-07-22 15:22:13 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:14 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap
2025-07-22 15:22:14 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:14 - plugin.httpLoadSimulator - INFO - 测试项目 '契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw' 执行成功
2025-07-22 15:22:14 - plugin.httpLoadSimulator - INFO - 执行测试项目 9/17: Milesight VPN 路径穿越漏洞-2023hw
2025-07-22 15:22:14 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:14 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8096
2025-07-22 15:22:14 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:14 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/Milesight VPN 路径穿越漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8096
2025-07-22 15:22:16 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8096/../etc/passwd completed with status code 200
2025-07-22 15:22:18 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:20 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/Milesight VPN 路径穿越漏洞-2023hw.pcap
2025-07-22 15:22:20 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:20 - plugin.httpLoadSimulator - INFO - 测试项目 'Milesight VPN 路径穿越漏洞-2023hw' 执行成功
2025-07-22 15:22:20 - plugin.httpLoadSimulator - INFO - 执行测试项目 10/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw
2025-07-22 15:22:20 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:20 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8097
2025-07-22 15:22:20 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:20 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8097
2025-07-22 15:22:22 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8097/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=4723--&password=1 completed with status code 200
2025-07-22 15:22:24 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:25 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap
2025-07-22 15:22:25 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:25 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw' 执行成功
2025-07-22 15:22:25 - plugin.httpLoadSimulator - INFO - 执行测试项目 11/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw
2025-07-22 15:22:25 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:25 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8098
2025-07-22 15:22:25 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:25 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8098
2025-07-22 15:22:27 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8098/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=78451--&password=1 completed with status code 200
2025-07-22 15:22:29 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:31 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap
2025-07-22 15:22:31 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:31 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw' 执行成功
2025-07-22 15:22:31 - plugin.httpLoadSimulator - INFO - 执行测试项目 12/17: 锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw
2025-07-22 15:22:31 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:31 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8099
2025-07-22 15:22:31 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:31 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8099
2025-07-22 15:22:33 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8099/ijklmnop.php completed with status code 200
2025-07-22 15:22:35 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:37 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap
2025-07-22 15:22:37 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:37 - plugin.httpLoadSimulator - INFO - 测试项目 '锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw' 执行成功
2025-07-22 15:22:37 - plugin.httpLoadSimulator - INFO - 执行测试项目 13/17: 海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw
2025-07-22 15:22:37 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:37 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 15:22:37 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:37 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 15:22:39 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/center/api/files;.jsp completed with status code 200
2025-07-22 15:22:41 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:42 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap
2025-07-22 15:22:42 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:42 - plugin.httpLoadSimulator - INFO - 测试项目 '海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw' 执行成功
2025-07-22 15:22:42 - plugin.httpLoadSimulator - INFO - 执行测试项目 14/17: 通达OA /module/upload/upload.php 任意文件上传-2-2023hw
2025-07-22 15:22:42 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:42 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 15:22:42 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:42 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 15:22:44 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/module/upload/upload.php?module=im completed with status code 200
2025-07-22 15:22:46 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:48 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap
2025-07-22 15:22:48 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:48 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-2-2023hw' 执行成功
2025-07-22 15:22:48 - plugin.httpLoadSimulator - INFO - 执行测试项目 15/17: 通达OA /module/upload/upload.php 任意文件上传-1-2023hw
2025-07-22 15:22:48 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:48 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 15:22:48 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:48 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 15:22:50 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/general/system/attachment/position/add.php completed with status code 200
2025-07-22 15:22:52 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:53 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap
2025-07-22 15:22:53 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:53 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-1-2023hw' 执行成功
2025-07-22 15:22:53 - plugin.httpLoadSimulator - INFO - 执行测试项目 16/17: 深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw
2025-07-22 15:22:53 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:53 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8091
2025-07-22 15:22:53 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:53 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8091
2025-07-22 15:22:55 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8091/153287 completed with status code 200
2025-07-22 15:22:57 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:22:59 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap
2025-07-22 15:22:59 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:22:59 - plugin.httpLoadSimulator - INFO - 测试项目 '深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw' 执行成功
2025-07-22 15:22:59 - plugin.httpLoadSimulator - INFO - 执行测试项目 17/17: 东方通TongWeb 安装路径敏感信息泄露-2023hw
2025-07-22 15:22:59 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 15:22:59 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8092
2025-07-22 15:22:59 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 15:22:59 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8092
2025-07-22 15:23:01 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8092/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 15:23:03 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 15:23:04 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/afc81b7d-de20-450b-a2f6-ddb6d40bf372/东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap
2025-07-22 15:23:04 - plugin.httpLoadSimulator - ERROR - the second tcprewrite failed with exit code 255, result:  Fatal Error: couldn't open /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/uploads/frag.conf
2025-07-22 15:23:04 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 安装路径敏感信息泄露-2023hw' 执行成功
2025-07-22 15:23:04 - plugin.httpLoadSimulator - INFO - 批量任务 afc81b7d-de20-450b-a2f6-ddb6d40bf372 执行完成，成功: 17个，失败: 0个
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: Fastjson_代码执行_漏洞验证.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: Milesight VPN 路径穿越漏洞-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 15:23:57 - plugin.httpLoadSimulator - INFO - 成功创建任务报文压缩包: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/http_load_report_afc81b7d-de20-450b-a2f6-ddb6d40bf372.zip，包含 17 个pcap文件
2025-07-22 16:26:52 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 16:26:52 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 16:26:52 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 16:26:54 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 16:26:54 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 16:26:54 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 开始处理批量任务创建请求
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 接收到参数: taskName=test1, targetIp=12*******, targetPort=8088, csvFile=test.csv
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 开始执行批量任务: ed8f05a8-cb0f-48a9-b761-44615cbd4219，共17个测试项目
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 检测到CSV文件编码: GB2312
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 成功使用编码 GB2312 解析CSV文件，共17个测试项目
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 执行测试项目 1/17: Fastjson_代码执行_漏洞验证
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:27:22 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/Fastjson_代码执行_漏洞验证.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 16:27:24 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/web/admin/sysDictionaryInfo/getListByDictionaryName completed with status code 200
2025-07-22 16:27:26 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:27:28 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/Fastjson_代码执行_漏洞验证.pcap
2025-07-22 16:27:28 - plugin.httpLoadSimulator - INFO - 测试项目 'Fastjson_代码执行_漏洞验证' 执行成功
2025-07-22 16:27:28 - plugin.httpLoadSimulator - INFO - 执行测试项目 2/17: 东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw
2025-07-22 16:27:28 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:27:28 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 16:27:28 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:27:28 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 16:27:30 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/console/service completed with status code 200
2025-07-22 16:27:32 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:27:33 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap
2025-07-22 16:27:33 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw' 执行成功
2025-07-22 16:27:33 - plugin.httpLoadSimulator - INFO - 执行测试项目 3/17: 东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw
2025-07-22 16:27:33 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:27:33 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 16:27:33 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:27:33 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 16:27:35 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 16:27:37 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:27:39 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap
2025-07-22 16:27:39 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw' 执行成功
2025-07-22 16:27:39 - plugin.httpLoadSimulator - INFO - 执行测试项目 4/17: 东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw
2025-07-22 16:27:39 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:27:39 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8091
2025-07-22 16:27:39 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:27:39 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8091
2025-07-22 16:27:41 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8091/console/css/5f8f4ab7.jsp completed with status code 200
2025-07-22 16:27:43 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:27:45 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 16:27:45 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 16:27:45 - plugin.httpLoadSimulator - INFO - 执行测试项目 5/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw
2025-07-22 16:27:45 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:27:45 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8092
2025-07-22 16:27:45 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:27:45 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8092
2025-07-22 16:27:47 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8092/console/css/abcd.jsp completed with status code 200
2025-07-22 16:27:49 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:27:50 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 16:27:50 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 16:27:50 - plugin.httpLoadSimulator - INFO - 执行测试项目 6/17: 东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw
2025-07-22 16:27:50 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:27:50 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8093
2025-07-22 16:27:50 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:27:50 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8093
2025-07-22 16:27:52 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8093/heimdall/deploy/upload?method=upload completed with status code 200
2025-07-22 16:27:54 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:27:56 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 16:27:56 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /deploy/upload 接口存在任意文件上传漏洞-1-2023hw' 执行成功
2025-07-22 16:27:56 - plugin.httpLoadSimulator - INFO - 执行测试项目 7/17: 东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw
2025-07-22 16:27:56 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:27:56 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8094
2025-07-22 16:27:56 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:27:56 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8094
2025-07-22 16:27:58 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8094/heimdall/abcd.jsp completed with status code 200
2025-07-22 16:28:00 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:01 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 16:28:01 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb /selectApp.jsp 路径存在任意文件上传漏洞-2-2023hw' 执行成功
2025-07-22 16:28:01 - plugin.httpLoadSimulator - INFO - 执行测试项目 8/17: 契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw
2025-07-22 16:28:01 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:01 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8095
2025-07-22 16:28:01 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:01 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8095
2025-07-22 16:28:03 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8095/callback/%2E%2E;/code/download?codeId=12345 completed with status code 200
2025-07-22 16:28:06 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:07 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap
2025-07-22 16:28:07 - plugin.httpLoadSimulator - INFO - 测试项目 '契约锁-电子签章系统 /code/upload 接口存在任意文件上传-2-2023hw' 执行成功
2025-07-22 16:28:07 - plugin.httpLoadSimulator - INFO - 执行测试项目 9/17: Milesight VPN 路径穿越漏洞-2023hw
2025-07-22 16:28:07 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:07 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8096
2025-07-22 16:28:07 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:07 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/Milesight VPN 路径穿越漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8096
2025-07-22 16:28:09 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8096/../etc/passwd completed with status code 200
2025-07-22 16:28:11 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:13 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/Milesight VPN 路径穿越漏洞-2023hw.pcap
2025-07-22 16:28:13 - plugin.httpLoadSimulator - INFO - 测试项目 'Milesight VPN 路径穿越漏洞-2023hw' 执行成功
2025-07-22 16:28:13 - plugin.httpLoadSimulator - INFO - 执行测试项目 10/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw
2025-07-22 16:28:13 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:13 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8097
2025-07-22 16:28:13 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:13 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8097
2025-07-22 16:28:15 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8097/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=4723--&password=1 completed with status code 200
2025-07-22 16:28:17 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:18 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap
2025-07-22 16:28:18 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw' 执行成功
2025-07-22 16:28:18 - plugin.httpLoadSimulator - INFO - 执行测试项目 11/17: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw
2025-07-22 16:28:18 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:18 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8098
2025-07-22 16:28:18 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:18 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8098
2025-07-22 16:28:20 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8098/servlet/com.sksoft.v8.trans.servlet.TaskRequestServlet?unitid=1%27%20or%2078451=78451--&password=1 completed with status code 200
2025-07-22 16:28:22 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:24 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap
2025-07-22 16:28:24 - plugin.httpLoadSimulator - INFO - 测试项目 '用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw' 执行成功
2025-07-22 16:28:24 - plugin.httpLoadSimulator - INFO - 执行测试项目 12/17: 锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw
2025-07-22 16:28:24 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:24 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8099
2025-07-22 16:28:24 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:24 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8099
2025-07-22 16:28:26 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8099/ijklmnop.php completed with status code 200
2025-07-22 16:28:28 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:29 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap
2025-07-22 16:28:29 - plugin.httpLoadSimulator - INFO - 测试项目 '锐捷EG网关 /ddi/server/fileupload.php 文件上传漏洞-2-2023hw' 执行成功
2025-07-22 16:28:29 - plugin.httpLoadSimulator - INFO - 执行测试项目 13/17: 海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw
2025-07-22 16:28:29 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:29 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8088
2025-07-22 16:28:29 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:29 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8088
2025-07-22 16:28:31 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8088/center/api/files;.jsp completed with status code 200
2025-07-22 16:28:33 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:35 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap
2025-07-22 16:28:35 - plugin.httpLoadSimulator - INFO - 测试项目 '海康威视-综合安防管理平台 /center/api/files 任意文件上传漏洞-2023hw' 执行成功
2025-07-22 16:28:35 - plugin.httpLoadSimulator - INFO - 执行测试项目 14/17: 通达OA /module/upload/upload.php 任意文件上传-2-2023hw
2025-07-22 16:28:35 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:35 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8089
2025-07-22 16:28:35 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:35 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8089
2025-07-22 16:28:37 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8089/module/upload/upload.php?module=im completed with status code 200
2025-07-22 16:28:39 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:40 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap
2025-07-22 16:28:41 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-2-2023hw' 执行成功
2025-07-22 16:28:41 - plugin.httpLoadSimulator - INFO - 执行测试项目 15/17: 通达OA /module/upload/upload.php 任意文件上传-1-2023hw
2025-07-22 16:28:41 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:41 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8090
2025-07-22 16:28:41 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:41 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8090
2025-07-22 16:28:43 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8090/general/system/attachment/position/add.php completed with status code 200
2025-07-22 16:28:45 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:46 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap
2025-07-22 16:28:46 - plugin.httpLoadSimulator - INFO - 测试项目 '通达OA /module/upload/upload.php 任意文件上传-1-2023hw' 执行成功
2025-07-22 16:28:46 - plugin.httpLoadSimulator - INFO - 执行测试项目 16/17: 深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw
2025-07-22 16:28:46 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:46 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8091
2025-07-22 16:28:46 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:46 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8091
2025-07-22 16:28:48 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8091/153287 completed with status code 200
2025-07-22 16:28:50 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:52 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap
2025-07-22 16:28:52 - plugin.httpLoadSimulator - INFO - 测试项目 '深信服应用交付系统 /rep/login 远程命令执行漏洞-2-2023hw' 执行成功
2025-07-22 16:28:52 - plugin.httpLoadSimulator - INFO - 执行测试项目 17/17: 东方通TongWeb 安装路径敏感信息泄露-2023hw
2025-07-22 16:28:52 - plugin.httpLoadSimulator - INFO - 开始HTTP负载模拟，目标: 12*******:8088
2025-07-22 16:28:52 - plugin.httpLoadSimulator - INFO - HTTP模拟服务器启动成功: 12*******:8092
2025-07-22 16:28:52 - plugin.httpLoadSimulator - INFO - HTTP负载解析成功
2025-07-22 16:28:52 - plugin.httpLoadSimulator - INFO - 启动tcpdump命令: tcpdump -i lo -w /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap -s 0 src host 12******* and dst host 12******* and port 8092
2025-07-22 16:28:54 - plugin.httpLoadSimulator - INFO - Request to http://12*******:8092/console/notinrealm/rest/commons/location completed with status code 200
2025-07-22 16:28:56 - plugin.httpLoadSimulator - INFO - tcpdump已停止
2025-07-22 16:28:57 - plugin.httpLoadSimulator - INFO - 抓包文件已生成: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/ed8f05a8-cb0f-48a9-b761-44615cbd4219/东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap
2025-07-22 16:28:57 - plugin.httpLoadSimulator - INFO - 测试项目 '东方通TongWeb 安装路径敏感信息泄露-2023hw' 执行成功
2025-07-22 16:28:57 - plugin.httpLoadSimulator - INFO - 批量任务 ed8f05a8-cb0f-48a9-b761-44615cbd4219 执行完成，成功: 17个，失败: 0个
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: Fastjson_代码执行_漏洞验证.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb deployupload 接口存在任意文件上传漏洞-1-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb selectAppjsp 路径存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb 安装路径敏感信息泄露-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb 后台存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 锐捷EG网关 ddiserverfileuploadphp 文件上传漏洞-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 用友时空KSOA软件 TaskRequestServlet SQL注入漏洞-1-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: Milesight VPN 路径穿越漏洞-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb remotecall 远程代码执行漏洞-1-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb remotecall 远程代码执行漏洞-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 通达OA moduleuploaduploadphp 任意文件上传-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 契约锁-电子签章系统 codeupload 接口存在任意文件上传-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 深信服应用交付系统 replogin 远程命令执行漏洞-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 通达OA moduleuploaduploadphp 任意文件上传-1-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 海康威视-综合安防管理平台 centerapifiles 任意文件上传漏洞-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 添加报文文件到压缩包: 东方通TongWeb deployupload 接口存在任意文件上传漏洞-2-2023hw.pcap
2025-07-22 16:29:18 - plugin.httpLoadSimulator - INFO - 成功创建任务报文压缩包: /home/<USER>/pythonproject/persion/tools/integrated_platform/plugins/httpLoadSimulator/reports/http_load_report_ed8f05a8-cb0f-48a9-b761-44615cbd4219.zip，包含 17 个pcap文件
2025-07-22 16:56:31 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 16:56:31 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 16:56:31 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:07:36 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:07:36 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:07:36 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:12:06 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:12:06 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:12:06 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:24:24 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:24:24 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:24:24 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
2025-07-22 17:25:25 - plugin.httpLoadSimulator - INFO - 正在初始化插件: HTTP负载模拟访问
2025-07-22 17:25:25 - plugin.httpLoadSimulator - INFO - HTTP负载模拟访问插件初始化完成
2025-07-22 17:25:25 - plugin.httpLoadSimulator - INFO - 插件初始化成功: HTTP负载模拟访问
