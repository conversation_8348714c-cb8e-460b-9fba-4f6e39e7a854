# -*- coding: utf-8 -*-
"""
HTTP负载模拟访问插件主类
"""

import os
import json
import uuid
import threading
import zipfile

from datetime import datetime
from typing import List, Dict, Any, Optional
from flask import render_template, request, jsonify, send_file, current_app

from core.base_plugin import BasePlugin
from .http_load_simulator import HttpLoadSimulator


class Plugin(BasePlugin):
    """HTTP负载模拟访问插件"""

    def __init__(self, **kwargs):
        # 先调用父类初始化
        super().__init__(**kwargs)
        
        # 设置插件信息
        self.name = "httpLoadSimulator"
        self.displayName = "HTTP负载模拟访问"
        self.description = "支持模拟发送自定义HTTP负载请求，生成网络报文抓包文件"
        self.version = "1.0.0"
        self.author = "Plugin Developer"
        
        # 初始化插件目录
        self.pluginDir = os.path.dirname(os.path.abspath(__file__))
        self.uploadsDir = os.path.join(self.pluginDir, 'uploads')
        self.reportsDir = os.path.join(self.pluginDir, 'reports')
        self.logsDir = os.path.join(self.pluginDir, 'logs')
        
        # 确保目录存在
        for directory in [self.uploadsDir, self.reportsDir, self.logsDir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
        
        # 任务管理
        self.tasks = {}
        self.taskLock = threading.Lock()
        self.tasksFile = os.path.join(self.pluginDir, 'tasks.json')
        self._loadTasks()

    def _loadTasks(self):
        """加载任务数据"""
        try:
            if os.path.exists(self.tasksFile):
                with open(self.tasksFile, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
        except Exception as e:
            self.logger.error(f"加载任务数据失败: {str(e)}")
            self.tasks = {}

    def _saveTasks(self):
        """保存任务数据"""
        try:
            with open(self.tasksFile, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存任务数据失败: {str(e)}")

    def getInfo(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': self.name,
            'displayName': self.displayName,
            'description': self.description,
            'version': self.version,
            'author': self.author
        }

    def getRoutes(self) -> List[Dict[str, Any]]:
        """获取页面路由配置"""
        return [
            {
                "rule": "/",
                "view_func": self.createTaskPage,
                "methods": ["GET"],
                "endpoint": "index"
            },
            {
                "rule": "/create-task",
                "view_func": self.createTaskPage,
                "methods": ["GET"],
                "endpoint": "create_task_page"
            },
            {
                "rule": "/task-results",
                "view_func": self.taskResultsPage,
                "methods": ["GET"],
                "endpoint": "task_results_page"
            }
        ]

    def getApiRoutes(self) -> List[Dict[str, Any]]:
        """获取API路由配置"""
        return [
            # 任务管理API
            {
                "rule": "/tasks",
                "view_func": self.getTasks,
                "methods": ["GET"],
                "endpoint": "get_tasks"
            },
            {
                "rule": "/tasks",
                "view_func": self.createTask,
                "methods": ["POST"],
                "endpoint": "create_task"
            },
            {
                "rule": "/batch-tasks",
                "view_func": self.createBatchTask,
                "methods": ["POST"],
                "endpoint": "create_batch_task"
            },
            {
                "rule": "/tasks/<task_id>/status",
                "view_func": self.getTaskStatus,
                "methods": ["GET"],
                "endpoint": "get_task_status"
            },
            {
                "rule": "/tasks/<task_id>/download-report",
                "view_func": self.downloadTaskReport,
                "methods": ["GET"],
                "endpoint": "download_task_report"
            },
            {
                "rule": "/tasks/<task_id>/delete",
                "view_func": self.deleteTask,
                "methods": ["DELETE"],
                "endpoint": "delete_task"
            },
            {
                "rule": "/tasks/batch-delete",
                "view_func": self.batchDeleteTasks,
                "methods": ["POST"],
                "endpoint": "batch_delete_tasks"
            }
        ]

    def getNavItems(self) -> List[Dict[str, Any]]:
        """获取导航项配置"""
        return [
            {
                "title": "HTTP负载模拟访问",
                "url": "/plugins/httpLoadSimulator/",
                "icon": "fas fa-server",
                "order": 40
            }
        ]

    def onInitialize(self):
        """插件初始化回调"""
        self.logger.info("HTTP负载模拟访问插件初始化完成")
        
        # 初始化HTTP负载模拟器
        self.httpLoadSimulator = HttpLoadSimulator(
            plugin_dir=self.pluginDir,
            logger=self.logger
        )

    def createTaskPage(self):
        """创建任务页面"""
        try:
            return render_template('httpLoadSimulator/create_task.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载创建任务页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def taskResultsPage(self):
        """任务结果页面"""
        try:
            return render_template('httpLoadSimulator/task_results.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载任务结果页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def getTasks(self):
        """获取任务列表"""
        try:
            with self.taskLock:
                taskList = []
                for taskId, task in self.tasks.items():
                    taskList.append({
                        'id': taskId,
                        'name': task.get('name', ''),
                        'status': task.get('status', 'unknown'),
                        'createTime': task.get('createTime', ''),
                        'finishTime': task.get('finishTime', ''),
                        'result': task.get('result', ''),
                        'hasReport': self._hasReportFiles(task.get('reportDir', ''), task)
                    })
                
                # 按创建时间倒序排序（最新的任务在前面）
                taskList.sort(key=lambda x: x.get('createTime', ''), reverse=True)
                
                return jsonify({
                    'success': True,
                    'data': taskList
                })
        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务列表失败: {str(e)}"
            }), 500

    def _hasReportFiles(self, reportDir: str, task: Dict[str, Any] = None) -> bool:
        """检查是否有有效的pcap报文文件"""
        if not reportDir or not os.path.exists(reportDir):
            return False

        # 检查报告目录中是否有有效的pcap文件（文件大小大于0）
        for file in os.listdir(reportDir):
            if file.endswith('.pcap'):
                filePath = os.path.join(reportDir, file)
                if os.path.exists(filePath) and os.path.getsize(filePath) > 0:
                    return True
        return False

    def createTask(self):
        """创建HTTP负载模拟任务"""
        try:
            # 获取JSON数据
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '请求数据格式错误'
                }), 400

            taskName = data.get('taskName', '').strip()
            httpPayload = data.get('httpPayload', '').strip()
            targetIp = data.get('targetIp', '').strip()
            targetPort = data.get('targetPort', 80)

            # 验证参数
            if not taskName:
                return jsonify({
                    'success': False,
                    'message': '任务名称不能为空'
                }), 400

            if not httpPayload:
                return jsonify({
                    'success': False,
                    'message': 'HTTP负载不能为空'
                }), 400

            if not targetIp:
                return jsonify({
                    'success': False,
                    'message': '目标IP地址不能为空'
                }), 400

            # 生成任务ID
            taskId = str(uuid.uuid4())

            # 创建任务记录
            task = {
                'id': taskId,
                'name': taskName,
                'httpPayload': httpPayload,
                'targetIp': targetIp,
                'targetPort': int(targetPort),
                'status': 'pending',
                'createTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'finishTime': '',
                'result': '',
                'reportDir': os.path.join(self.reportsDir, taskId)
            }

            with self.taskLock:
                self.tasks[taskId] = task
                self._saveTasks()

            # 启动后台任务
            thread = threading.Thread(
                target=self._executeTask,
                args=(taskId,),
                daemon=True
            )
            thread.start()

            return jsonify({
                'success': True,
                'message': '任务创建成功',
                'data': {'taskId': taskId}
            })

        except Exception as e:
            self.logger.error(f"创建任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"创建任务失败: {str(e)}"
            }), 500

    def createBatchTask(self):
        """创建批量HTTP负载模拟任务"""
        try:
            # 获取表单数据
            taskName = request.form.get('taskName', '').strip()
            csvFile = request.files.get('csvFile')

            # 验证参数
            if not taskName:
                return jsonify({
                    'success': False,
                    'message': '任务名称不能为空'
                }), 400

            if not csvFile:
                return jsonify({
                    'success': False,
                    'message': '请上传CSV文件'
                }), 400

            # 验证文件类型
            if not csvFile.filename.lower().endswith('.csv'):
                return jsonify({
                    'success': False,
                    'message': '请上传CSV格式的文件'
                }), 400

            # 生成批量任务ID
            batchTaskId = str(uuid.uuid4())

            # 创建批量任务目录
            batchTaskDir = os.path.join(self.reportsDir, batchTaskId)
            os.makedirs(batchTaskDir, exist_ok=True)

            # 保存CSV文件
            csvFilePath = os.path.join(batchTaskDir, 'batch_data.csv')
            csvFile.save(csvFilePath)

            # 解析CSV文件
            csvData = self._parseCsvFile(csvFilePath)
            if not csvData:
                return jsonify({
                    'success': False,
                    'message': 'CSV文件格式错误或为空'
                }), 400

            # 创建批量任务记录
            batchTask = {
                'id': batchTaskId,
                'name': taskName,
                'type': 'batch',
                'status': 'pending',
                'createTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'finishTime': '',
                'result': '',
                'reportDir': batchTaskDir,
                'csvFilePath': csvFilePath,
                'totalItems': len(csvData),
                'completedItems': 0,
                'failedItems': 0
            }

            with self.taskLock:
                self.tasks[batchTaskId] = batchTask
                self._saveTasks()

            # 启动批量任务执行
            thread = threading.Thread(
                target=self._executeBatchTask,
                args=(batchTaskId,),
                daemon=True
            )
            thread.start()

            return jsonify({
                'success': True,
                'message': f'批量任务创建成功，共{len(csvData)}个测试项目',
                'data': {'taskId': batchTaskId}
            })

        except Exception as e:
            self.logger.error(f"创建批量任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"创建批量任务失败: {str(e)}"
            }), 500

    def getTaskStatus(self, task_id):
        """获取任务状态"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404

                return jsonify({
                    'success': True,
                    'data': {
                        'id': task['id'],
                        'name': task['name'],
                        'status': task['status'],
                        'result': task['result'],
                        'hasReport': self._hasReportFiles(task.get('reportDir', ''), task)
                    }
                })
        except Exception as e:
            self.logger.error(f"获取任务状态失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务状态失败: {str(e)}"
            }), 500

    def _executeTask(self, task_id):
        """执行HTTP负载模拟任务"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return

                task['status'] = 'running'
                self._saveTasks()

            # 确保报告目录存在
            os.makedirs(task['reportDir'], exist_ok=True)

            # 执行HTTP负载模拟
            result = self.httpLoadSimulator.simulateHttpLoad(
                http_payload=task['httpPayload'],
                target_ip=task['targetIp'],
                target_port=task['targetPort'],
                output_dir=task['reportDir']
            )

            with self.taskLock:
                task['status'] = 'completed' if result['success'] else 'failed'
                task['result'] = result['message']
                task['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self._saveTasks()

        except Exception as e:
            self.logger.error(f"执行任务失败: {str(e)}")
            with self.taskLock:
                if task_id in self.tasks:
                    self.tasks[task_id]['status'] = 'failed'
                    self.tasks[task_id]['result'] = f'任务执行失败: {str(e)}'
                    self.tasks[task_id]['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()

    def _executeBatchTask(self, batch_task_id):
        """执行批量HTTP负载模拟任务（串行执行）"""
        try:
            with self.taskLock:
                batchTask = self.tasks.get(batch_task_id)
                if not batchTask:
                    return

                batchTask['status'] = 'running'
                self._saveTasks()

            self.logger.info(f"开始执行批量任务: {batch_task_id}，共{batchTask['totalItems']}个测试项目")

            # 确保批量任务报告目录存在
            os.makedirs(batchTask['reportDir'], exist_ok=True)

            completedCount = 0
            failedCount = 0

            # 从保存的CSV文件读取数据
            csvFilePath = batchTask.get('csvFilePath', '')
            if not csvFilePath or not os.path.exists(csvFilePath):
                self.logger.error(f"CSV文件不存在: {csvFilePath}")
                with self.taskLock:
                    batchTask['status'] = 'failed'
                    batchTask['result'] = 'CSV文件不存在'
                    batchTask['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()
                return

            csvData = self._parseCsvFile(csvFilePath)
            if not csvData:
                self.logger.error(f"解析CSV文件失败: {csvFilePath}")
                with self.taskLock:
                    batchTask['status'] = 'failed'
                    batchTask['result'] = '解析CSV文件失败'
                    batchTask['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()
                return

            # 串行执行每个测试项目
            for index, item in enumerate(csvData):
                try:
                    testName = item.get('testName', f'测试项目{index + 1}')
                    httpPayload = item.get('httpPayload', '')

                    self.logger.info(f"执行测试项目 {index + 1}/{len(csvData)}: {testName}")

                    # 清理测试项目名称，用作文件名
                    cleanTestName = "".join(c for c in testName if c.isalnum() or c in (' ', '-', '_')).strip()
                    if not cleanTestName:
                        cleanTestName = f'test_{index + 1}'

                    # 执行HTTP负载模拟，pcap文件直接保存到批量任务目录
                    result = self.httpLoadSimulator.simulateHttpLoad(
                        http_payload=httpPayload,
                        target_ip='127.0.0.1',  # 默认使用本地IP
                        target_port=8080,       # 默认端口
                        output_dir=batchTask['reportDir'],
                        pcap_filename_prefix=cleanTestName  # 使用测试项目名称作为pcap文件前缀
                    )

                    if result['success']:
                        completedCount += 1
                        self.logger.info(f"测试项目 '{testName}' 执行成功")
                    else:
                        failedCount += 1
                        self.logger.error(f"测试项目 '{testName}' 执行失败: {result['message']}")

                    # 更新批量任务进度
                    with self.taskLock:
                        batchTask['completedItems'] = completedCount
                        batchTask['failedItems'] = failedCount
                        self._saveTasks()

                except Exception as e:
                    failedCount += 1
                    self.logger.error(f"执行测试项目 '{testName}' 失败: {str(e)}")

                    # 更新批量任务进度
                    with self.taskLock:
                        batchTask['completedItems'] = completedCount
                        batchTask['failedItems'] = failedCount
                        self._saveTasks()

            # 完成批量任务
            with self.taskLock:
                batchTask['status'] = 'completed'
                batchTask['result'] = f'批量任务完成，成功: {completedCount}个，失败: {failedCount}个'
                batchTask['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self._saveTasks()

            self.logger.info(f"批量任务 {batch_task_id} 执行完成，成功: {completedCount}个，失败: {failedCount}个")

        except Exception as e:
            self.logger.error(f"执行批量任务失败: {str(e)}")
            with self.taskLock:
                if batch_task_id in self.tasks:
                    self.tasks[batch_task_id]['status'] = 'failed'
                    self.tasks[batch_task_id]['result'] = f'批量任务执行失败: {str(e)}'
                    self.tasks[batch_task_id]['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()

    def downloadTaskReport(self, task_id):
        """下载任务报文压缩包"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404

                reportDir = task.get('reportDir', '')
                if not self._hasReportFiles(reportDir, task):
                    return jsonify({
                        'success': False,
                        'message': '任务报文文件不存在'
                    }), 500

                zipPath = self._createTaskReportZip(task_id, task, reportDir)
                if not zipPath:
                    return jsonify({
                        'success': False,
                        'message': '创建报文压缩包失败'
                    }), 500

                return send_file(
                    zipPath,
                    as_attachment=True,
                    download_name=f"http_load_report_{task_id}.zip"
                )
        except Exception as e:
            self.logger.error(f"下载任务报文压缩包失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"下载失败: {str(e)}"
            }), 500

    def deleteTask(self, task_id):
        """删除任务"""
        try:
            with self.taskLock:
                if task_id not in self.tasks:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404

                task = self.tasks[task_id]
                reportDir = task.get('reportDir', '')

                # 删除报告目录
                if reportDir and os.path.exists(reportDir):
                    import shutil
                    shutil.rmtree(reportDir)

                # 删除任务记录
                del self.tasks[task_id]
                self._saveTasks()

            return jsonify({
                'success': True,
                'message': '任务删除成功'
            })
        except Exception as e:
            self.logger.error(f"删除任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"删除任务失败: {str(e)}"
            }), 500

    def batchDeleteTasks(self):
        """批量删除任务"""
        try:
            data = request.get_json()
            if not data or 'taskIds' not in data:
                return jsonify({
                    'success': False,
                    'message': '请求参数错误'
                }), 400

            taskIds = data['taskIds']
            if not isinstance(taskIds, list):
                return jsonify({
                    'success': False,
                    'message': '任务ID列表格式错误'
                }), 400

            deletedCount = 0
            with self.taskLock:
                for taskId in taskIds:
                    if taskId in self.tasks:
                        task = self.tasks[taskId]
                        reportDir = task.get('reportDir', '')

                        # 删除报告目录
                        if reportDir and os.path.exists(reportDir):
                            import shutil
                            shutil.rmtree(reportDir)

                        # 删除任务记录
                        del self.tasks[taskId]
                        deletedCount += 1

                self._saveTasks()

            return jsonify({
                'success': True,
                'message': f'成功删除 {deletedCount} 个任务',
                'data': {'deletedCount': deletedCount}
            })
        except Exception as e:
            self.logger.error(f"批量删除任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"批量删除任务失败: {str(e)}"
            }), 500

    def _createTaskReportZip(self, task_id: str, task: Dict[str, Any], reportDir: str) -> str:
        """创建任务报文压缩包，只包含pcap报文文件"""
        try:
            # 创建临时压缩文件
            zipPath = os.path.join(self.reportsDir, f"http_load_report_{task_id}.zip")

            # 如果压缩文件已存在，先删除
            if os.path.exists(zipPath):
                try:
                    os.remove(zipPath)
                    self.logger.info(f"删除已存在的压缩文件: {zipPath}")
                except Exception as e:
                    self.logger.warning(f"删除已存在压缩文件失败: {str(e)}")

            with zipfile.ZipFile(zipPath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                pcapCount = 0

                # 直接添加报告目录中的所有pcap文件
                if os.path.exists(reportDir):
                    for filename in os.listdir(reportDir):
                        if filename.endswith('.pcap'):
                            filePath = os.path.join(reportDir, filename)
                            if os.path.exists(filePath) and os.path.getsize(filePath) > 0:
                                # 直接使用原文件名，已经包含了测试项目名称前缀
                                zipf.write(filePath, filename)
                                pcapCount += 1
                                self.logger.info(f"添加报文文件到压缩包: {filename}")

                if pcapCount == 0:
                    self.logger.warning(f"任务 {task_id} 没有找到有效的pcap报文文件")
                    return None

            self.logger.info(f"成功创建任务报文压缩包: {zipPath}，包含 {pcapCount} 个pcap文件")
            return zipPath

        except Exception as e:
            self.logger.error(f"创建任务报文压缩包失败: {str(e)}")
            return None

    def _parseCsvFile(self, csvFilePath: str) -> List[Dict[str, str]]:
        """解析CSV文件"""
        try:
            import csv
            csvData = []

            with open(csvFilePath, 'r', encoding='utf-8') as file:
                # 尝试检测CSV方言
                sample = file.read(1024)
                file.seek(0)
                sniffer = csv.Sniffer()
                dialect = sniffer.sniff(sample)

                reader = csv.reader(file, dialect)

                for rowIndex, row in enumerate(reader, 1):
                    if len(row) >= 2:
                        testName = row[0].strip()
                        httpPayload = row[1].strip()

                        if testName and httpPayload:
                            csvData.append({
                                'testName': testName,
                                'httpPayload': httpPayload,
                                'rowIndex': rowIndex
                            })

            self.logger.info(f"成功解析CSV文件，共{len(csvData)}个测试项目")
            return csvData

        except Exception as e:
            self.logger.error(f"解析CSV文件失败: {str(e)}")
            return []
