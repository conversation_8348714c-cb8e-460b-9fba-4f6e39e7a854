{% extends "base.html" %}

{% block title %}{{ pluginInfo.displayName }} - 创建任务{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='plugins/httpLoadSimulator/css/style.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- 主要内容区域 -->
    <div class="http-load-simulator-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="header-icon">
                <i class="fas fa-server"></i>
            </div>
            <div class="header-content">
                <h2>新建HTTP负载模拟任务</h2>
                <p>配置HTTP负载参数后启动模拟任务，支持自定义HTTP请求头和请求体，生成完整的网络报文抓包文件</p>
            </div>
        </div>

        <!-- 任务配置面板 -->
        <div class="panel config-panel">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="panel-title">
                    <h3>任务配置</h3>
                    <p>设置HTTP负载模拟参数</p>
                </div>
            </div>
            <div class="panel-body">
                <form id="createTaskForm" onsubmit="handleFormSubmit(event)">
                    <!-- 任务名称 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tag"></i>
                            <span>任务名称</span>
                        </label>
                        <input type="text"
                               class="form-input"
                               id="taskName"
                               placeholder="请输入任务名称"
                               required>
                        <div class="form-hint">请输入一个有意义的任务名称，便于后续识别和管理</div>
                    </div>

                    <!-- 目标配置 -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-bullseye"></i>
                                <span>目标IP地址</span>
                            </label>
                            <input type="text"
                                   class="form-input"
                                   id="targetIp"
                                   placeholder="例如：*************"
                                   required>
                            <div class="form-hint">目标服务器的IP地址</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-plug"></i>
                                <span>目标端口</span>
                            </label>
                            <input type="number"
                                   class="form-input"
                                   id="targetPort"
                                   value="80"
                                   min="1"
                                   max="65535"
                                   required>
                            <div class="form-hint">目标服务端口号（1-65535）</div>
                        </div>
                    </div>

                    <!-- HTTP负载 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-code"></i>
                            <span>HTTP负载</span>
                        </label>
                        <textarea class="form-textarea"
                                  id="httpPayload"
                                  rows="15"
                                  placeholder="请输入完整的HTTP请求，例如：&#10;POST /api/test HTTP/1.1&#10;Host: example.com&#10;Content-Type: application/json&#10;Content-Length: 25&#10;&#10;{&quot;key&quot;: &quot;value&quot;}"
                                  required></textarea>
                        <div class="form-hint">请输入完整的HTTP请求，包括请求行、请求头和请求体</div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-play"></i>
                            <span>开始模拟</span>
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i>
                            <span>重置表单</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 任务状态面板 -->
        <div class="panel status-panel" id="statusPanel" style="display: none;">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="panel-title">
                    <h3>任务执行状态</h3>
                    <p>实时显示任务执行进度和结果</p>
                </div>
            </div>
            <div class="panel-body">
                <div class="status-content">
                    <div class="status-item">
                        <div class="status-label">任务ID：</div>
                        <div class="status-value" id="taskId">-</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">任务状态：</div>
                        <div class="status-value" id="taskStatus">
                            <span class="status-badge status-pending">等待中</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">执行结果：</div>
                        <div class="status-value" id="taskResult">-</div>
                    </div>
                </div>
                
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>

                <div class="action-buttons" id="actionButtons" style="display: none;">
                    <button class="btn btn-success" id="downloadBtn" onclick="downloadResult()">
                        <i class="fas fa-download"></i>
                        <span>下载抓包文件</span>
                    </button>
                    <button class="btn btn-info" onclick="viewResults()">
                        <i class="fas fa-eye"></i>
                        <span>查看结果</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示框 -->
    <div class="alert-container" id="alertContainer"></div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='plugins/httpLoadSimulator/js/create_task.js') }}"></script>
{% endblock %}
