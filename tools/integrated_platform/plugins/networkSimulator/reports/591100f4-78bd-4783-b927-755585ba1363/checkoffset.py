import base64

# 编码
def encodeDoubleBytes(s:str):
    if isinstance(s,str):
        s = s.encode('utf8')
    ret = b''
    for b in s:
        ret += (0xc0 | b>>6).to_bytes(1,'little')
        ret += (0x80 | b&0x3f).to_bytes(1,'little')
    return ret

def checkDoubleBytesOffset(s, t):
    assert isinstance(s,str) and isinstance(t,str)
    return base64.b64decode(s).index(encodeDoubleBytes(t))

def checkRawBytes(s, t):
    if isinstance(t, str):
        t = t.encode()
    return base64.b64decode(s).index(t)

def checkFileDouble(filenmae, t, offset=-1):
    with open(filenmae, 'r') as f:
        content = f.read()
    if offset > 0:
        return checkDoubleBytesOffset(content[:offset],t)
    else:
        return checkDoubleBytesOffset(content,t)

def checkFileRaw(filenmae, t, offset=-1):
    with open(filenmae, 'r') as f:
        content = f.read()
    if offset > 0:
        return checkRawBytes(content[:offset],t)
    else:
        return checkRawBytes(content,t)