# save hit results in hit_results.txt or hit_results.csv
import pandas
import pathlib
from io import BytesIO

def loadHitLines(filepath):
    filepath = pathlib.Path(filepath)
    with filepath.open('rb') as f:
        content = f.read()
    try:
        bytesData = BytesIO(content)
        df = pandas.read_csv(bytesData)
        return set(list(df['威胁名称']))
    except:
        pass
    lines = [i for i in content.decode().split('\n') if len(i) > 0]
    return set(lines)

def prepareCompiledRules():
    ruleFile = pathlib.Path("remote_rules_dir/generated_rules.rules")
    with ruleFile.open('r') as f:
        lines = f.read().split('\n')
    ret = set()
    for line in lines:
        if len(line) == 0 :
            continue
        name = line.split('";')[0].split(':"')[1]
        ret.add(name)
    return ret

if __name__ == "__main__":
    import sys
    hits = loadHitLines(sys.argv[1])
    compiledRules = prepareCompiledRules()
    rightHit = set()
    missHit = set()
    for hit in hits:
        if hit in compiledRules:
            print(f"right hit rule: {hit}")
            rightHit.add(hit)
    print()
    print(f"hit count: {len(rightHit)/len(compiledRules)}")
    print()
    
    for allRule in compiledRules:
        if allRule not in hits:
            missHit.add(allRule)
            print(f"missed rule: {allRule}")