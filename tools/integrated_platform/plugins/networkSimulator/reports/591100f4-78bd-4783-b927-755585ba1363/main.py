from configs import *
from targets import sequences, patchDecodeSizes, invalidRules

generated_ids = []

def generateRawRules(seqName:str, sequences: list):
    global startSid
    raw_contents = ''
    for sequence in sequences:
        raw_contents += f'content:"{sequence}";distance:0;'
    rule = rule_template.format(**{
        'ruleName': seqName + " Detection",
        'header': headers['normal'],
        'sequenceContent': raw_contents,
        'sid': startSid
    })
    generated_ids.append(startSid)
    startSid += 1
    return rule

def generateBase64EncodeRule(seqName:str, sequences:list):
    global startSid, patched_count
    raw_contents = ''
    for sequence in sequences:
        raw_contents += f'base64_data; content:"{sequence}";distance:0;'
    rulename = seqName + " Detection - Base64 Encoding"
    if not checkValid(rulename):
        return ""
    rule = rule_template.format(**{
        'ruleName': rulename,
        'header': headers['base64Encoding'],
        'sequenceContent': raw_contents,
        'sid': startSid
    })
    if rulename in patchDecodeSizes.keys():
        print(rulename, "patched size to ",int(patchDecodeSizes[rulename]))
        rule = rule.replace("base64_decode:bytes 1000,", f"base64_decode:bytes {int(patchDecodeSizes[rulename])},")
        patched_count += 1
        patchDecodeSizes.pop(rulename)
    generated_ids.append(startSid)
    startSid += 1
    return rule

def generateDoubleEncodingRawSeq(seqName:str, sequences:list):
    global startSid
    raw_contents = ''
    for sequence in sequences:
        sequence = encodeDoubleBytes(sequence)
        raw_contents += sequence
    # ToDo Debuing mode show different rulename..
    # If product version, make same name to avoid reverse engineering.
    rulename = seqName + " Detection - DoubleByte Encoding"
    if not checkValid(rulename):
        return ""
    rule = rule_template.format(**{
        'ruleName': rulename,
        'header': headers['normal'],
        'sequenceContent': raw_contents,
        'sid': startSid
    })
    generated_ids.append(startSid)
    startSid += 1
    return rule

patched_count = 0

def checkValid(rulename):
    if rulename in invalidRules.keys():
        print(f'invalid rule: {rulename} for current engine. do not add.')
        invalidRules.pop(rulename)
        return False
    else:
        return True

def generateDoubleEncodingBase64Seq(seqName:str, sequences:list):
    global startSid, patched_count
    raw_contents = ''
    for sequence in sequences:
        sequence = encodeDoubleBytes(sequence)
        raw_contents += f'base64_data; content:"{sequence}"; distance:0;'
    rulename = seqName + " Detection - DoubleByte Base64 Encoding"
    if not checkValid(rulename):
        return ""
    rule = rule_template.format(**{
        'ruleName': rulename,
        'header': headers['base64Encoding'],
        'sequenceContent': raw_contents,
        'sid': startSid
    })
    if rulename in patchDecodeSizes.keys():
        print(rulename, "patched size to ",int(patchDecodeSizes[rulename]))
        org_rule = rule
        rule = rule.replace("base64_decode:bytes 1000,", f"base64_decode:bytes {int(patchDecodeSizes[rulename])},")
        if org_rule == rule:
            raise Exception("rule pathch failed.")
        patched_count += 1
        patchDecodeSizes.pop(rulename)
    generated_ids.append(startSid)
    startSid += 1
    return rule

def transIdToIdProtMap(id_:str):
    return str(id_) + ":TCP HTTP"

finalRules = []

for name, seq in sequences.items():
    finalRules.append(
        generateRawRules(name,seq)
    )
    finalRules.append(
        generateDoubleEncodingRawSeq(name,seq)
    )
    finalRules.append(
        generateBase64EncodeRule(name,seq)
    )
    # finalRules.append(
    #     generateDoubleEncodingBase64Seq(name,seq)
    # )

# # debug printout 
# for line in finalRules:
#     print(line)
patch_rate =  patched_count/(len(patchDecodeSizes) + patched_count)

if patch_rate < 1 :
    print(f"patched rate: {patch_rate}.")
    print(f"unpatched keys: {list(patchDecodeSizes.keys())}")
else:
    print("all patched.")

if len(invalidRules) > 0:
    print("invalid rules are not removed: ", invalidRules)

# save into files.
with open('./remote_rules_dir/generated_rules.rules','w') as f:
    finalRules = [i for i in finalRules if len(i) > 0]
    f.write('\n'.join(finalRules))
print(f"generated rules: {len(finalRules)} at file: ./remote_rules_dir/generated_rules.rules")

with open('./remote_rules_dir/generate_ids.txt','w') as f:
    f.write('\n'.join(list(map(transIdToIdProtMap,generated_ids))))
print("generated id:protocol lists at file: ./remote_rules_dir/generated_rules.rules")
