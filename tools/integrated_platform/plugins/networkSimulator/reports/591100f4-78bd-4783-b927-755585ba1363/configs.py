startSid = 44087
rule_template = """alert TCP any any -> any any (msg:"{ruleName}";flow:to_server; {header} {sequenceContent} sid:{sid};)"""

def encodeDoubleBytesRaw(s:str):
    if isinstance(s,str):
        s = s.encode('utf8')
    ret = b''
    for b in s:
        ret += (0xc0 | b>>6).to_bytes(1,'little')
        ret += (0x80 | b&0x3f).to_bytes(1,'little')
    return ret

def encodeDoubleBytes(s:str):
    if isinstance(s,str):
        s = s.encode('utf8')
    ret = b''
    for b in s:
        ret += (0xc0 | b>>6).to_bytes(1,'little')
        ret += (0x80 | b&0x3f).to_bytes(1,'little')
    ret_str = ""
    for i in ret:
        c = hex(i)[2:]
        if len(ret_str)>0:
            ret_str += " "
        ret_str += c
    return "|" + ret_str + "|"

headers = {
   "normal": """content:"|ac ed|"; offset:0;depth:3000; """,
   "base64Encoding": """content:"rO0A"; base64_decode:bytes 1000,offset 0,relative; """
}

seqEncodingFuncs = {
    "normal": str,
    "doubleEncodeing": encodeDoubleBytes
}
