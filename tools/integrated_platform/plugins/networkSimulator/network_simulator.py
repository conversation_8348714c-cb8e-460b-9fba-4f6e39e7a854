# -*- coding: utf-8 -*-
"""
网络模拟访问核心功能模块
"""

import os
import socket
import subprocess
import threading
import time
import signal
import requests
from urllib.parse import urlparse
from typing import List, Dict, Any, Optional
import logging
import random
import json
from socketserver import ThreadingTCPServer, ThreadingUDPServer, BaseRequestHandler, ThreadingMixIn
from http.server import HTTPServer, BaseHTTPRequestHandler
import struct
import binascii
import shlex
import ipaddress


class ThreadingHTTPServer(ThreadingMixIn, HTTPServer):
    """支持多线程的HTTP服务器，改善连接处理"""
    allow_reuse_address = True
    daemon_threads = True

    def __init__(self, server_address, RequestHandlerClass):
        super().__init__(server_address, RequestHandlerClass)
        # 设置socket选项以改善连接处理
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        # 设置TCP_NODELAY以减少延迟
        self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)


class ImprovedThreadingTCPServer(ThreadingTCPServer):
    """改进的多线程TCP服务器，支持更好的连接处理"""
    allow_reuse_address = True
    daemon_threads = True

    def __init__(self, server_address, RequestHandlerClass):
        super().__init__(server_address, RequestHandlerClass)
        # 设置socket选项以改善连接处理
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        # 设置TCP_NODELAY以减少延迟
        self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        # 设置SO_LINGER以确保连接正确关闭
        self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_LINGER, struct.pack('ii', 1, 0))


class MockHTTPHandler(BaseHTTPRequestHandler):
    """模拟HTTP服务器处理器"""

    def do_GET(self):
        """处理GET请求"""
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.send_header('Server', 'NetworkSimulator/1.0')
        self.send_header('Connection', 'close')  # 明确指示关闭连接
        self.end_headers()

        response_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>模拟服务器响应</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>网络模拟访问 - 服务器响应</h1>
            <p>请求路径: {self.path}</p>
            <p>请求方法: {self.command}</p>
            <p>客户端地址: {self.client_address[0]}:{self.client_address[1]}</p>
            <p>时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <hr>
            <p>这是一个模拟的HTTP服务器响应，用于网络流量分析。</p>
        </body>
        </html>
        """.encode('utf-8')

        self.wfile.write(response_body)
        # 确保数据发送完毕后关闭连接
        self.wfile.flush()

    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Server', 'NetworkSimulator/1.0')
        self.send_header('Connection', 'close')  # 明确指示关闭连接
        self.end_headers()

        response_data = {
            'status': 'success',
            'message': '请求处理成功',
            'path': self.path,
            'method': self.command,
            'client_ip': self.client_address[0],
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'received_data_length': len(post_data)
        }

        self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
        # 确保数据发送完毕后关闭连接
        self.wfile.flush()

    def finish(self):
        """完成请求处理，确保连接正确关闭"""
        try:
            # 确保所有数据都已发送
            if hasattr(self, 'wfile') and self.wfile:
                self.wfile.flush()
            # 调用父类的finish方法
            super().finish()
        except Exception:
            # 忽略关闭时的异常
            pass

    def handle(self):
        """重写handle方法，确保连接管理正确"""
        try:
            super().handle()
        except Exception:
            # 忽略处理异常，确保连接能够正确关闭
            pass
        finally:
            # 确保socket连接关闭
            try:
                if hasattr(self, 'connection') and self.connection:
                    self.connection.close()
            except Exception:
                pass

    def log_message(self, format, *args):
        """重写日志方法，避免输出到stderr"""
        pass


class MockTCPHandler(BaseRequestHandler):
    """模拟TCP服务器处理器"""

    def setup(self):
        """设置连接"""
        try:
            # 设置socket选项以改善连接处理
            self.request.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            self.request.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            # 设置接收超时
            self.request.settimeout(30.0)
        except Exception:
            pass

    def handle(self):
        """处理TCP连接"""
        try:
            # 接收客户端数据
            data = self.request.recv(1024)
            if not data:
                return  # 客户端已关闭连接

            # 检测是否收到FIN（连接关闭信号）
            if len(data) == 0:
                return

            # 模拟HTTP响应
            if data.startswith(b'GET') or data.startswith(b'POST'):
                response_body = (
                    b"TCP Mock Server Response\n"
                    b"Received data length: " + str(len(data)).encode() + b"\n"
                    b"Timestamp: " + time.strftime('%Y-%m-%d %H:%M:%S').encode() + b"\n"
                )
                response = (
                    b"HTTP/1.1 200 OK\r\n"
                    b"Content-Type: text/plain\r\n"
                    b"Server: NetworkSimulator-TCP/1.0\r\n"
                    b"Connection: close\r\n"
                    b"Content-Length: " + str(len(response_body)).encode() + b"\r\n"
                    b"\r\n" + response_body
                )
            else:
                # 普通TCP响应
                response = (
                    b"TCP-ECHO: " + data +
                    b"\nServer: NetworkSimulator-TCP/1.0\n" +
                    b"Timestamp: " + time.strftime('%Y-%m-%d %H:%M:%S').encode() + b"\n"
                )

            # 发送响应数据
            self.request.sendall(response)

        except socket.timeout:
            # 超时，正常关闭连接
            pass
        except ConnectionResetError:
            # 客户端重置连接
            pass
        except Exception:
            # 其他异常，忽略
            pass

    def finish(self):
        """完成连接处理，确保连接正确关闭"""
        try:
            # 优雅关闭写端，发送FIN
            self.request.shutdown(socket.SHUT_WR)
        except (OSError, AttributeError):
            # 忽略已关闭的socket错误
            pass
        finally:
            # 确保连接完全关闭
            try:
                self.request.close()
            except Exception:
                pass


class MockDNSHandler(BaseRequestHandler):
    """模拟DNS服务器处理器"""

    def handle(self):
        """处理DNS查询"""
        try:
            data, socket_obj = self.request
            if len(data) < 12:  # DNS头部最小长度
                return

            # 解析DNS查询
            query_info = self._parseDnsQuery(data)
            if not query_info:
                return

            # 生成DNS响应
            response = self._createDnsResponse(data, query_info)
            if response:
                socket_obj.sendto(response, self.client_address)

        except Exception as e:
            pass  # 忽略DNS处理错误

    def _parseDnsQuery(self, data: bytes) -> Dict[str, Any]:
        """解析DNS查询"""
        try:
            # DNS头部解析
            header = struct.unpack('!HHHHHH', data[:12])
            query_id = header[0]
            flags = header[1]
            qdcount = header[2]

            if qdcount == 0:
                return None

            # 解析查询域名
            offset = 12
            domain_parts = []
            while offset < len(data):
                length = data[offset]
                if length == 0:
                    offset += 1
                    break
                if length > 63:  # 压缩指针
                    offset += 2
                    break
                domain_parts.append(data[offset + 1:offset + 1 + length].decode('utf-8'))
                offset += 1 + length

            if offset + 4 > len(data):
                return None

            # 查询类型和类
            qtype, qclass = struct.unpack('!HH', data[offset:offset + 4])

            return {
                'query_id': query_id,
                'flags': flags,
                'domain': '.'.join(domain_parts),
                'qtype': qtype,
                'qclass': qclass,
                'original_data': data
            }

        except Exception:
            return None

    def _createDnsResponse(self, query_data: bytes, query_info: Dict[str, Any]) -> bytes:
        """创建DNS响应"""
        try:
            domain = query_info['domain']
            query_id = query_info['query_id']
            qtype = query_info['qtype']

            # DNS响应头部
            flags = 0x8180  # 标准查询响应，无错误
            qdcount = 1     # 查询数量
            ancount = 1     # 回答数量
            nscount = 0     # 权威记录数量
            arcount = 0     # 附加记录数量

            response = struct.pack('!HHHHHH', query_id, flags, qdcount, ancount, nscount, arcount)

            # 复制查询部分
            query_section = query_data[12:]
            response += query_section

            # 添加回答部分
            # 域名压缩指针指向查询部分的域名
            response += struct.pack('!H', 0xc00c)  # 压缩指针

            # 根据查询类型生成不同的响应
            if qtype == 1:  # A记录
                response += struct.pack('!HHIH', 1, 1, 300, 4)  # 类型A，类IN，TTL=300，长度4
                response += socket.inet_aton('*************')  # 模拟IP地址
            elif qtype == 28:  # AAAA记录
                response += struct.pack('!HHIH', 28, 1, 300, 16)  # 类型AAAA，类IN，TTL=300，长度16
                response += socket.inet_pton(socket.AF_INET6, '1000::1')  # 模拟IPv6地址
            elif qtype == 15:  # MX记录
                response += struct.pack('!HHIH', 15, 1, 300, 8)  # 类型MX，类IN，TTL=300，长度8
                response += struct.pack('!H', 10)  # 优先级
                response += struct.pack('!H', 0xc00c)  # 指向域名
            else:  # 其他类型，返回A记录
                response += struct.pack('!HHIH', 1, 1, 300, 4)
                response += socket.inet_aton('*************')

            return response

        except Exception:
            return None


class NetworkSimulator:
    """网络模拟访问器"""
    
    def __init__(self, plugin_dir: str, logger: logging.Logger):
        self.pluginDir = plugin_dir
        self.logger = logger
        self.tcpdumpProcess = None
        self.tcpdumpLock = threading.Lock()

        # 服务端管理
        self.mockServers = {}  # 存储启动的模拟服务器
        self.serverLock = threading.Lock()
        self.basePort = 18000  # 模拟服务器起始端口
    
    def _isIpAddress(self, ip: str) -> bool:
        """
        判断给定的字符串是否为有效的IP地址
        """
        try:
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False
        
    def simulateNetworkAccess(self, targets: List[str], protocol: str, port: int, output_dir: str) -> Dict[str, Any]:
        """
        模拟网络访问并抓包

        Args:
            targets: 目标地址列表（IP或域名）
            protocol: 协议类型（http, https, tcp, udp）
            port: 端口号
            output_dir: 输出pcap文件目录

        Returns:
            Dict: 执行结果
        """
        try:
            self.logger.info(f"开始网络模拟访问，目标: {targets}, 协议: {protocol}, 端口: {port}")

            # 启动模拟服务器
            mock_servers = self._startMockServers(targets, protocol, port)

            # 执行网络访问模拟，每个目标单独抓包
            results = []
            pcap_files = []
            pcapProcess=pcapReplay(self.logger)
            count=1
            for target in targets:
                # 为每个目标创建单独的抓包文件
                target_pcap = os.path.join(output_dir, f"{target.replace('.', '_').replace(':', '_')}.pcap")
                pcap_files.append(target_pcap)

                # 启动tcpdump抓包
                if not self._startTcpdump(port, target_pcap):
                    self.logger.warning(f"目标 {target} 启动抓包失败")
                    results.append({
                        'target': target,
                        'success': False,
                        'message': '启动抓包失败'
                    })
                    continue

                # 等待tcpdump启动
                time.sleep(2)

                # 执行单个目标的网络访问模拟
                result = self._simulateTargetWithMockServer(target, protocol, port, mock_servers)
                results.append(result)

                # 停止当前目标的tcpdump
                time.sleep(1)
                self._stopTcpdump()

                if os.path.exists(target_pcap):
                    self.logger.info(f"目标 {target} 抓包完成，文件: {target_pcap}")
                else:
                    self.logger.warning(f"目标 {target} 抓包失败")

                # 使用IP地址自增长函数，避免生成非法IP地址
                srcIp = pcapProcess._generate_ip_address('*********', count - 1)
                if self._isIpAddress(target):
                    dstIp = target
                else:
                    dstIp = pcapProcess._generate_ip_address('*********', count - 1)
                cache,newPcap=pcapProcess.rewrite(srcIp=srcIp, dstIp=dstIp,pcapPath=target_pcap,port=port)
                if cache and os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                if newPcap and os.path.exists(target_pcap):
                        try:
                            os.remove(target_pcap)
                            os.rename(newPcap,target_pcap)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup old pcap file {cache}: {str(cleanup_e)}")
                

                # 每次访问间隔一段时间
                time.sleep(1)
                count+=1



            # 停止模拟服务器
            self._stopMockServers(mock_servers)

            # 统计结果
            successCount = sum(1 for r in results if r['success'])
            totalCount = len(results)

            return {
                'success': True,
                'message': f'网络模拟完成，成功访问 {successCount}/{totalCount} 个目标，包含完整请求-响应交互',
                'details': results,
                'pcap_files': pcap_files
            }

        except Exception as e:
            self.logger.error(f"网络模拟访问失败: {str(e)}")
            self._stopTcpdump()
            self._stopMockServers(mock_servers if 'mock_servers' in locals() else [])
            return {
                'success': False,
                'message': f'网络模拟访问失败: {str(e)}'
            }

    def simulateFileTransfer(self, files: List[Dict[str, Any]], protocol: str, port: int, output_dir: str, target_ip: str = "127.0.0.1", target_domain: str = "testfileserver") -> Dict[str, Any]:
        """
        模拟文件传输并抓包

        Args:
            files: 文件列表，每个文件包含name, path, size信息
            protocol: 协议类型 (目前只支持http)
            port: 目标端口
            output_dir: 输出pcap文件目录
            target_ip: 目标IP地址（默认127.0.0.1）
            target_domain: 目标域名（用于Host头，默认testfileserver）

        Returns:
            Dict: 包含成功状态和消息的字典
        """
        try:
            self.logger.info(f"开始文件传输模拟，文件数量: {len(files)}, 协议: {protocol}, 端口: {port}")

            # 启动HTTP模拟服务器，使用动态端口避免冲突
            mock_port = self._findAvailablePort(port)
            mock_server = self._startHttpMockServer('localhost', mock_port)
            if not mock_server:
                return {
                    'success': False,
                    'message': 'HTTP模拟服务器启动失败'
                }

            # 执行文件传输模拟，每个文件单独抓包
            results = []
            pcap_files = []
            pcapProcess=pcapReplay(self.logger)
            count=1

            for file_info in files:
                filename = file_info['name']
                filepath = file_info['path']

                # 为每个文件创建单独的抓包文件
                safe_filename = filename.replace('.', '_').replace('/', '_').replace('\\', '_')
                file_pcap = os.path.join(output_dir, f"{safe_filename}.pcap")
                pcap_files.append(file_pcap)

                # 启动tcpdump抓包
                if not self._startTcpdump(mock_port, file_pcap):
                    self.logger.warning(f"文件 {filename} 启动抓包失败")
                    results.append({
                        'file': filename,
                        'success': False,
                        'message': '启动抓包失败'
                    })
                    continue

                # 等待tcpdump启动
                time.sleep(2)

                # 执行单个文件的HTTP传输模拟
                result = self._simulateHttpFileTransfer(file_info, target_ip, target_domain, mock_port)
                results.append(result)
                time.sleep(2)
                
                

                # 停止当前文件的tcpdump
                self._stopTcpdump()

                # 使用IP地址自增长函数，避免生成非法IP地址
                srcIp = pcapProcess._generate_ip_address('*********', count - 1)
                dstIp = pcapProcess._generate_ip_address('*********', count - 1)
                cache,newPcap=pcapProcess.rewrite(srcIp=srcIp, dstIp=dstIp,pcapPath=file_pcap,port=mock_port)
                if cache and os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                if newPcap and os.path.exists(file_pcap):
                        try:
                            os.remove(file_pcap)
                            os.rename(newPcap,file_pcap)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup old pcap file {cache}: {str(cleanup_e)}")
                

                # 每次访问间隔一段时间
                time.sleep(1)
                count+=1

                

            # 停止HTTP模拟服务器
            if mock_server:
                mock_server.shutdown()
                mock_server.server_close()

            # 统计结果
            successCount = sum(1 for r in results if r['success'])
            totalCount = len(results)

            return {
                'success': True,
                'message': f'文件传输模拟完成，成功传输 {successCount}/{totalCount} 个文件，包含完整HTTP交互',
                'details': results,
                'pcap_files': pcap_files
            }

        except Exception as e:
            self.logger.error(f"文件传输模拟失败: {str(e)}")
            return {
                'success': False,
                'message': f'文件传输模拟失败: {str(e)}'
            }

    def _findAvailablePort(self, start_port: int) -> int:
        """查找可用端口"""
        import socket

        for port in range(start_port, start_port + 100):  # 尝试100个端口
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue

        # 如果都不可用，返回一个随机端口
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', 0))
            return s.getsockname()[1]

    def _simulateHttpFileTransfer(self, file_info: Dict[str, Any], target_ip: str, target_domain: str, target_port: int) -> Dict[str, Any]:
        """模拟HTTP文件传输到指定IP，但Host头显示域名"""
        try:
            filename = file_info['name']
            filepath = file_info['path']
            filesize = file_info['size']

            # 读取文件内容
            with open(filepath, 'rb') as f:
                file_content = f.read()

            # 创建HTTP POST请求模拟文件上传
            import requests

            # 模拟向目标IP上传文件，但Host头显示域名
            files = {'file': (filename, file_content)}
            data = {'filename': filename, 'size': filesize}

            # 自定义请求头，设置Host为域名
            headers = {
                'Host': target_domain,
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            try:
                # 请求URL使用IP地址，但Host头使用域名
                response = requests.post(
                    f'http://{target_ip}:{target_port}/upload',
                    files=files,
                    data=data,
                    headers=headers,
                    timeout=10
                )

                return {
                    'file': filename,
                    'success': True,
                    'message': f'文件 {filename} 传输成功到 {target_ip}，Host: {target_domain}，大小: {filesize} 字节，响应状态: {response.status_code}',
                    'details': {
                        'filename': filename,
                        'size': filesize,
                        'target_ip': target_ip,
                        'host_header': target_domain,
                        'status_code': response.status_code
                    }
                }

            except requests.exceptions.RequestException as e:
                return {
                    'file': filename,
                    'success': True,  # 即使请求失败，也算成功，因为生成了网络流量
                    'message': f'文件 {filename} 传输模拟完成到 {target_ip}，Host: {target_domain}，大小: {filesize} 字节（模拟网络交互）',
                    'details': {
                        'filename': filename,
                        'size': filesize,
                        'target_ip': target_ip,
                        'host_header': target_domain,
                        'note': '模拟HTTP文件传输交互'
                    }
                }

        except Exception as e:
            return {
                'file': file_info.get('name', 'unknown'),
                'success': False,
                'message': f'文件传输失败: {str(e)}'
            }
    
    def _startTcpdump(self,port,output_file: str) -> bool:
        """启动tcpdump抓包"""
        try:
            with self.tcpdumpLock:
                if self.tcpdumpProcess:
                    return True

                # 检查tcpdump是否可用
                try:
                    subprocess.run(['which', 'tcpdump'], check=True, capture_output=True)
                except subprocess.CalledProcessError:
                    self.logger.warning("tcpdump未安装，将创建模拟抓包文件")
                    return self._createMockPcapFile(output_file)

                # 构建tcpdump命令
                cmd = [
                    'tcpdump',
                    '-i', 'lo',  # 监听lo接口
                    '-w', output_file,  # 输出文件
                    '-s', '0',  # 抓取完整数据包
                    'src host','127.0.0.1',
                    'and','dst host','127.0.0.1',
                    'and','port', f'{port}'  #抓取指定端口的数据包
                ]

                self.logger.info(f"启动tcpdump命令: {' '.join(cmd)}")

                # 启动tcpdump进程
                self.tcpdumpProcess = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    preexec_fn=os.setsid
                )

                return True

        except Exception as e:
            self.logger.error(f"启动tcpdump失败: {str(e)}")
            # 如果tcpdump启动失败，创建模拟文件
            return self._createMockPcapFile(output_file)

    def _createMockPcapFile(self, output_file: str) -> bool:
        """创建模拟的pcap文件"""
        try:
            # 创建一个简单的pcap文件头
            pcap_header = bytes([
                0xD4, 0xC3, 0xB2, 0xA1,  # magic number
                0x02, 0x00,              # version major
                0x04, 0x00,              # version minor
                0x00, 0x00, 0x00, 0x00,  # thiszone
                0x00, 0x00, 0x00, 0x00,  # sigfigs
                0xFF, 0xFF, 0x00, 0x00,  # snaplen
                0x01, 0x00, 0x00, 0x00   # network (Ethernet)
            ])

            with open(output_file, 'wb') as f:
                f.write(pcap_header)

            self.logger.info(f"创建模拟pcap文件: {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"创建模拟pcap文件失败: {str(e)}")
            return False

    def _startMockServers(self, targets: List[str], protocol: str, port: int) -> List[Dict[str, Any]]:
        """启动模拟服务器"""
        mock_servers = []

        try:
            with self.serverLock:
                #for i, target in enumerate(targets):
                mock_port = port
                target='127.0.0.1'

                if protocol.lower() in ['http', 'https']:
                        # 启动HTTP模拟服务器
                        server = self._startHttpMockServer(target, mock_port)
                        if server:
                            mock_servers.append({
                                'target': target,
                                'protocol': protocol,
                                'original_port': port,
                                'mock_port': mock_port,
                                'server': server,
                                'type': 'http'
                            })
                            self.logger.info(f"HTTP模拟服务器启动成功: {target} -> 127.0.0.1:{mock_port}")

                elif protocol.lower() == 'tcp':
                        # 启动TCP模拟服务器
                        server = self._startTcpMockServer(target, mock_port)
                        if server:
                            mock_servers.append({
                                'target': target,
                                'protocol': protocol,
                                'original_port': port,
                                'mock_port': mock_port,
                                'server': server,
                                'type': 'tcp'
                            })
                            self.logger.info(f"TCP模拟服务器启动成功: {target} -> 127.0.0.1:{mock_port}")

                elif protocol.lower() == 'udp':
                        # UDP不需要持续的服务器，在访问时直接模拟
                        mock_servers.append({
                            'target': target,
                            'protocol': protocol,
                            'original_port': port,
                            'mock_port': mock_port,
                            'server': None,
                            'type': 'udp'
                        })
                        self.logger.info(f"UDP模拟配置: {target} -> 127.0.0.1:{mock_port}")

                elif protocol.lower() == 'dns':
                        # 启动DNS模拟服务器
                        server = self._startDnsMockServer(target, mock_port)
                        if server:
                            mock_servers.append({
                                'target': target,
                                'protocol': protocol,
                                'original_port': port,
                                'mock_port': mock_port,
                                'server': server,
                                'type': 'dns'
                            })
                            self.logger.info(f"DNS模拟服务器启动成功: {target} -> 127.0.0.1:{mock_port}")
                    

                return mock_servers

        except Exception as e:
            self.logger.error(f"启动模拟服务器失败: {str(e)}")
            self._stopMockServers(mock_servers)
            return []

    def _startHttpMockServer(self, target: str, port: int) -> Optional[ThreadingHTTPServer]:
        """启动HTTP模拟服务器"""
        try:
            server = ThreadingHTTPServer(('127.0.0.1', port), MockHTTPHandler)
            # 设置服务器属性以改善连接处理
            server.timeout = 30  # 设置超时时间

            server_thread = threading.Thread(target=server.serve_forever, daemon=True)
            server_thread.start()
            self.logger.info(f"HTTP模拟服务器启动成功: 127.0.0.1:{port}")
            return server
        except Exception as e:
            self.logger.error(f"启动HTTP模拟服务器失败 {target}:{port} - {str(e)}")
            return None

    def _startTcpMockServer(self, target: str, port: int) -> Optional[ImprovedThreadingTCPServer]:
        """启动TCP模拟服务器"""
        try:
            server = ImprovedThreadingTCPServer(('127.0.0.1', port), MockTCPHandler)
            # 设置服务器属性以改善连接处理
            server.timeout = 30  # 设置超时时间

            server_thread = threading.Thread(target=server.serve_forever, daemon=True)
            server_thread.start()
            self.logger.info(f"TCP模拟服务器启动成功: 127.0.0.1:{port}")
            return server
        except Exception as e:
            self.logger.error(f"启动TCP模拟服务器失败 {target}:{port} - {str(e)}")
            return None

    def _startDnsMockServer(self, target: str, port: int) -> Optional[ThreadingUDPServer]:
        """启动DNS模拟服务器"""
        try:
            server = ThreadingUDPServer(('127.0.0.1', port), MockDNSHandler)
            server_thread = threading.Thread(target=server.serve_forever, daemon=True)
            server_thread.start()
            return server
        except Exception as e:
            self.logger.error(f"启动DNS模拟服务器失败 {target}:{port} - {str(e)}")
            return None

    def _stopMockServers(self, mock_servers: List[Dict[str, Any]]):
        """停止模拟服务器"""
        try:
            with self.serverLock:
                for server_info in mock_servers:
                    server = server_info.get('server')
                    if server:
                        try:
                            server.shutdown()
                            server.server_close()
                            self.logger.info(f"模拟服务器已停止: {server_info['target']} ({server_info['type']})")
                        except Exception as e:
                            self.logger.error(f"停止模拟服务器失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"停止模拟服务器失败: {str(e)}")

    def _simulateTargetWithMockServer(self, target: str, protocol: str, port: int, mock_servers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        使用模拟服务器进行目标访问模拟

        Args:
            target: 目标地址
            protocol: 协议类型
            port: 端口号
            mock_servers: 模拟服务器列表

        Returns:
            Dict: 访问结果
        """
        try:
            self.logger.info(f"模拟访问目标: {target}, 协议: {protocol}, 端口: {port}")

            # 查找对应的模拟服务器
            mock_server = None
            if mock_servers:
                mock_server = mock_servers[0]

            if not mock_server:
                return {
                    'target': target,
                    'success': False,
                    'message': '未找到对应的模拟服务器'
                }

            if protocol.lower() in ['http', 'https']:
                return self._simulateHttpAccessWithMockServer(target, protocol, mock_server)
            elif protocol.lower() == 'tcp':
                return self._simulateTcpAccessWithMockServer(target, mock_server)
            elif protocol.lower() == 'udp':
                return self._simulateUdpAccessWithMockServer(target, mock_server)
            elif protocol.lower() == 'dns':
                return self._simulateDnsAccessWithMockServer(target, mock_server)
            else:
                return {
                    'target': target,
                    'success': False,
                    'message': f'不支持的协议: {protocol}'
                }

        except Exception as e:
            self.logger.error(f"模拟访问目标失败: {target}, 错误: {str(e)}")
            return {
                'target': target,
                'success': False,
                'message': f'访问失败: {str(e)}'
            }

    def _simulateHttpAccessWithMockServer(self, target: str, protocol: str, mock_server: Dict[str, Any]) -> Dict[str, Any]:
        """使用模拟服务器进行HTTP/HTTPS访问"""
        try:
            mock_port = mock_server['mock_port']

            # 构建本地URL
            scheme = 'http'  # 模拟服务器总是使用HTTP
            url = f"{scheme}://127.0.0.1:{mock_port}/"

            # 发送HTTP请求到模拟服务器
            response = requests.get(
                url,
                timeout=10,
                headers={
                    'Host': target,  # 设置Host头为原始目标
                    'User-Agent': 'NetworkSimulator/1.0',
                    'X-Original-Target': target,
                    'X-Original-Protocol': protocol,
                    'X-Original-Port': str(mock_server['original_port'])
                }
            )

            return {
                'target': target,
                'success': True,
                'message': f'HTTP访问成功，状态码: {response.status_code}，与模拟服务器完成完整交互',
                'details': {
                    'original_target': target,
                    'mock_server': f"127.0.0.1:{mock_port}",
                    'protocol': protocol,
                    'status_code': response.status_code,
                    'content_length': len(response.content),
                    'response_headers': dict(response.headers)
                }
            }

        except requests.exceptions.RequestException as e:
            return {
                'target': target,
                'success': False,
                'message': f'HTTP访问失败: {str(e)}'
            }

    def _simulateTcpAccessWithMockServer(self, target: str, mock_server: Dict[str, Any]) -> Dict[str, Any]:
        """使用模拟服务器进行TCP连接"""
        try:
            mock_port = mock_server['mock_port']

            # 创建TCP socket连接到模拟服务器
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)

            # 连接到模拟服务器
            sock.connect(('127.0.0.1', mock_port))

            # 发送测试数据
            test_data = f"GET / HTTP/1.1\r\nHost: {target}\r\nUser-Agent: NetworkSimulator/1.0\r\nX-Original-Target: {target}\r\n\r\n".encode()
            sock.send(test_data)

            # 接收响应
            response = sock.recv(4096)
            sock.close()

            return {
                'target': target,
                'success': True,
                'message': f'TCP连接成功，收到 {len(response)} 字节响应，完成完整交互',
                'details': {
                    'original_target': target,
                    'mock_server': f"127.0.0.1:{mock_port}",
                    'sent_bytes': len(test_data),
                    'received_bytes': len(response),
                    'response_preview': response[:200].decode('utf-8', errors='ignore')
                }
            }

        except Exception as e:
            return {
                'target': target,
                'success': False,
                'message': f'TCP连接异常: {str(e)}'
            }

    def _simulateUdpAccessWithMockServer(self, target: str, mock_server: Dict[str, Any]) -> Dict[str, Any]:
        """使用模拟服务器进行UDP通信"""
        try:
            mock_port = mock_server['mock_port']

            # 创建UDP socket
            client_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            client_sock.settimeout(5)

            # 启动临时UDP服务器来响应
            server_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            server_sock.bind(('127.0.0.1', mock_port))
            server_sock.settimeout(1)

            def udp_server():
                try:
                    data, addr = server_sock.recvfrom(1024)
                    response = f"UDP-ECHO: {data.decode('utf-8', errors='ignore')}\nServer: NetworkSimulator-UDP/1.0\nOriginal-Target: {target}\nTimestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n".encode()
                    server_sock.sendto(response, addr)
                except:
                    pass
                finally:
                    server_sock.close()

            # 启动UDP服务器线程
            server_thread = threading.Thread(target=udp_server, daemon=True)
            server_thread.start()

            time.sleep(0.1)  # 等待服务器启动

            # 发送UDP数据
            test_data = f"UDP test message to {target} from NetworkSimulator".encode()
            client_sock.sendto(test_data, ('127.0.0.1', mock_port))

            # 尝试接收响应
            try:
                response, addr = client_sock.recvfrom(1024)
                response_info = f"收到来自 {addr} 的 {len(response)} 字节响应"
                success = True
            except socket.timeout:
                response_info = "UDP发送成功，但无响应（超时）"
                response = b""
                success = True

            client_sock.close()

            return {
                'target': target,
                'success': success,
                'message': f'UDP通信完成，{response_info}，完成完整交互',
                'details': {
                    'original_target': target,
                    'mock_server': f"127.0.0.1:{mock_port}",
                    'sent_bytes': len(test_data),
                    'received_bytes': len(response),
                    'response_preview': response.decode('utf-8', errors='ignore')[:200] if response else 'No response'
                }
            }

        except Exception as e:
            return {
                'target': target,
                'success': False,
                'message': f'UDP通信失败: {str(e)}'
            }

    def _simulateDnsAccessWithMockServer(self, target: str, mock_server: Dict[str, Any]) -> Dict[str, Any]:
        """使用模拟服务器进行DNS查询"""
        try:
            mock_port = mock_server['mock_port']

            # 创建DNS查询包
            query_data = self._createDnsQuery(target)
            if not query_data:
                return {
                    'target': target,
                    'success': False,
                    'message': 'DNS查询包创建失败'
                }

            # 创建UDP socket进行DNS查询
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(5)

            try:
                # 发送DNS查询到模拟服务器
                sock.sendto(query_data, ('127.0.0.1', mock_port))

                # 接收DNS响应
                response_data, addr = sock.recvfrom(512)

                # 解析DNS响应
                response_info = self._parseDnsResponse(response_data)

                return {
                    'target': target,
                    'success': True,
                    'message': f'DNS查询成功，收到 {len(response_data)} 字节响应，完成完整交互',
                    'details': {
                        'original_target': target,
                        'mock_server': f"127.0.0.1:{mock_port}",
                        'query_bytes': len(query_data),
                        'response_bytes': len(response_data),
                        'response_info': response_info
                    }
                }

            except socket.timeout:
                return {
                    'target': target,
                    'success': False,
                    'message': 'DNS查询超时'
                }
            finally:
                sock.close()

        except Exception as e:
            return {
                'target': target,
                'success': False,
                'message': f'DNS查询失败: {str(e)}'
            }

    def _createDnsQuery(self, domain: str) -> bytes:
        """创建DNS查询包"""
        try:
            # DNS头部
            query_id = random.randint(1, 65535)
            flags = 0x0100  # 标准查询
            qdcount = 1     # 查询数量
            ancount = 0     # 回答数量
            nscount = 0     # 权威记录数量
            arcount = 0     # 附加记录数量

            header = struct.pack('!HHHHHH', query_id, flags, qdcount, ancount, nscount, arcount)

            # 查询部分
            query = b''
            for part in domain.split('.'):
                query += struct.pack('!B', len(part)) + part.encode('utf-8')
            query += b'\x00'  # 域名结束标志

            # 查询类型和类
            query += struct.pack('!HH', 1, 1)  # A记录，IN类

            return header + query

        except Exception:
            return None

    def _parseDnsResponse(self, data: bytes) -> Dict[str, Any]:
        """解析DNS响应"""
        try:
            if len(data) < 12:
                return {'error': '响应数据太短'}

            # 解析头部
            header = struct.unpack('!HHHHHH', data[:12])
            query_id = header[0]
            flags = header[1]
            qdcount = header[2]
            ancount = header[3]

            # 检查响应标志
            is_response = (flags & 0x8000) != 0
            rcode = flags & 0x000F

            result = {
                'query_id': query_id,
                'is_response': is_response,
                'response_code': rcode,
                'question_count': qdcount,
                'answer_count': ancount
            }

            if rcode == 0:
                result['status'] = 'success'
            else:
                result['status'] = 'error'
                result['error_code'] = rcode

            return result

        except Exception as e:
            return {'error': f'解析失败: {str(e)}'}

    def _stopTcpdump(self):
        """停止tcpdump抓包"""
        try:
            with self.tcpdumpLock:
                if self.tcpdumpProcess:
                    # 发送SIGTERM信号终止进程组
                    os.killpg(os.getpgid(self.tcpdumpProcess.pid), signal.SIGTERM)

                    # 等待进程结束
                    try:
                        self.tcpdumpProcess.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        # 如果5秒内没有结束，强制杀死
                        os.killpg(os.getpgid(self.tcpdumpProcess.pid), signal.SIGKILL)
                        self.tcpdumpProcess.wait()

                    self.tcpdumpProcess = None
                    self.logger.info("tcpdump进程已停止")
                else:
                    self.logger.info("tcpdump进程未运行或使用模拟模式")

        except Exception as e:
            self.logger.error(f"停止tcpdump失败: {str(e)}")
    
    def _simulateTarget(self, target: str, protocol: str, port: int) -> Dict[str, Any]:
        """
        模拟访问单个目标
        
        Args:
            target: 目标地址
            protocol: 协议类型
            port: 端口号
            
        Returns:
            Dict: 访问结果
        """
        try:
            self.logger.info(f"模拟访问目标: {target}, 协议: {protocol}, 端口: {port}")
            
            if protocol.lower() in ['http', 'https']:
                return self._simulateHttpAccess(target, protocol, port)
            elif protocol.lower() == 'tcp':
                return self._simulateTcpAccess(target, port)
            elif protocol.lower() == 'udp':
                return self._simulateUdpAccess(target, port)
            else:
                return {
                    'target': target,
                    'success': False,
                    'message': f'不支持的协议: {protocol}'
                }
                
        except Exception as e:
            self.logger.error(f"模拟访问目标失败: {target}, 错误: {str(e)}")
            return {
                'target': target,
                'success': False,
                'message': f'访问失败: {str(e)}'
            }
    
    def _simulateHttpAccess(self, target: str, protocol: str, port: int) -> Dict[str, Any]:
        """模拟HTTP/HTTPS访问"""
        try:
            # 构建URL
            if not target.startswith(('http://', 'https://')):
                scheme = protocol.lower()
                if port != (80 if scheme == 'http' else 443):
                    url = f"{scheme}://{target}:{port}"
                else:
                    url = f"{scheme}://{target}"
            else:
                url = target
            
            # 发送HTTP请求
            response = requests.get(
                url,
                timeout=10,
                verify=False,  # 忽略SSL证书验证
                allow_redirects=True
            )
            
            return {
                'target': target,
                'success': True,
                'message': f'HTTP访问成功，状态码: {response.status_code}',
                'details': {
                    'url': url,
                    'status_code': response.status_code,
                    'content_length': len(response.content)
                }
            }
            
        except requests.exceptions.RequestException as e:
            return {
                'target': target,
                'success': False,
                'message': f'HTTP访问失败: {str(e)}'
            }
    
    def _simulateTcpAccess(self, target: str, port: int) -> Dict[str, Any]:
        """模拟TCP连接"""
        try:
            # 创建TCP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            
            # 尝试连接
            result = sock.connect_ex((target, port))
            
            if result == 0:
                # 连接成功，发送一些测试数据
                test_data = b"GET / HTTP/1.1\r\nHost: " + target.encode() + b"\r\n\r\n"
                sock.send(test_data)
                
                # 尝试接收响应
                try:
                    response = sock.recv(1024)
                    response_info = f"收到 {len(response)} 字节响应"
                except:
                    response_info = "无响应数据"
                
                sock.close()
                
                return {
                    'target': target,
                    'success': True,
                    'message': f'TCP连接成功，{response_info}',
                    'details': {
                        'port': port,
                        'connected': True
                    }
                }
            else:
                sock.close()
                return {
                    'target': target,
                    'success': False,
                    'message': f'TCP连接失败，错误码: {result}'
                }
                
        except Exception as e:
            return {
                'target': target,
                'success': False,
                'message': f'TCP连接异常: {str(e)}'
            }
    
    def _simulateUdpAccess(self, target: str, port: int) -> Dict[str, Any]:
        """模拟UDP通信"""
        try:
            # 创建UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(5)
            
            # 发送测试数据
            test_data = b"UDP test message from network simulator"
            sock.sendto(test_data, (target, port))
            
            # 尝试接收响应
            try:
                response, addr = sock.recvfrom(1024)
                response_info = f"收到来自 {addr} 的 {len(response)} 字节响应"
                success = True
            except socket.timeout:
                response_info = "UDP发送成功，但无响应（正常情况）"
                success = True
            except Exception as e:
                response_info = f"UDP通信异常: {str(e)}"
                success = False
            
            sock.close()
            
            return {
                'target': target,
                'success': success,
                'message': f'UDP通信完成，{response_info}',
                'details': {
                    'port': port,
                    'sent_bytes': len(test_data)
                }
            }
            
        except Exception as e:
            return {
                'target': target,
                'success': False,
                'message': f'UDP通信失败: {str(e)}'
            }

class pcapReplay(object):
    '''报文回放到DUT'''
    def __init__(self,logger, task_id=None):
        self.name = 'pcap_replay'
        self.pacps = []
        self.task_id = task_id
        self.logger = logger
        
    
    def rewrite(self, **kwargs):
        try:
            # 参数验证
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']
            port= kwargs.get('port', 80)

            # 验证输入文件是否存在
            if not os.path.exists(pcapPath):
                raise FileNotFoundError(f"Input pcap file not found: {pcapPath}")

            dstMac = '00:1C:00:00:00:01'
            srcMac1 = '10:1C:12:B8:58:C2'
            srcMac2 = '01:1C:00:21:DE:DB'

            # 安全地处理文件路径
            if '.' not in pcapPath:
                self.logger.error(f"Invalid pcap file path format: {pcapPath}")
                return None, None

            pcapName = pcapPath[:pcapPath.rfind('.')]
            cache = pcapName + '.cache'
            outputfile = pcapName + '_out.pcap'
            PATH=os.path.dirname(os.path.abspath(__file__))
            fragConf=os.path.join(PATH, "uploads", "frag.conf") # 定义报文分片的配置文件，规定了tcprewrite时对报文分片的大小

            # 执行tcprewrite命令修复报文小于最低长度字节的问题
            tcprewrite = f'tcprewrite --fixlen=pad  --fixcsum  -i {shlex.quote(pcapPath)} -o {shlex.quote(pcapPath)}'
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None, None

            # 执行tcpprep命令
            tcpprep = f'tcpprep -p -i {shlex.quote(pcapPath)} -o {shlex.quote(cache)}'
            try:
                r = subprocess.getstatusoutput(tcpprep)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")

                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcpprep command: {str(e)}")
                return None, None

            # 执行tcprewrite命令
            tcprewrite = f'tcprewrite --fixlen=pad  --fixcsum  --fragroute={fragConf} --enet-dmac={dstMac}:{dstMac} --enet-smac={srcMac1}:{srcMac2} --endpoints={srcIp}:{dstIp} -i {shlex.quote(pcapPath)} -c {shlex.quote(cache)} -o {shlex.quote(outputfile)}'
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'the second tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    # 清理可能创建的pcap文件
                    if os.path.exists(outputfile):
                        try:
                            os.remove(outputfile)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup pcap file {outputfile}: {str(cleanup_e)}")
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None, None

            # 再次执行tcpprep命令，根据tcprewrite后输出的报文进行重新生成客户端和服务器区分的cache，避免报文分片后cache文件没有更新
            tcpprep = f'tcpprep -a client -i {shlex.quote(outputfile)} -o {shlex.quote(cache)}'
            try:
                r = subprocess.getstatusoutput(tcpprep)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    # 清理可能创建的pcap文件
                    if os.path.exists(outputfile):
                        try:
                            os.remove(outputfile)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup pcap file {outputfile}: {str(cleanup_e)}")
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcpprep command: {str(e)}")
                return None, None

            return cache, outputfile

        except Exception as e:
            self.logger.error(f"Error in pcapReplay.rewrite: {str(e)}")
            return None, None

    def replay(self, **kwargs):
        try:
            # 参数验证
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']
            interface1 = kwargs.get('interface1', '')
            interface2 = kwargs.get('interface2') if kwargs.get('interface2') else interface1

            # 验证网络接口参数
            if not interface1:
                self.logger.error("Network interface1 is required for packet replay")
                return False

            # 调用rewrite方法
            cache, pcapNew = self.rewrite(srcIp=srcIp, dstIp=dstIp, pcapPath=pcapPath)

            # 检查rewrite是否成功
            if cache is None or pcapNew is None:
                self.logger.error("Failed to rewrite pcap file, cannot proceed with replay")
                return False

            # 验证生成的文件是否存在
            if not os.path.exists(cache) or not os.path.exists(pcapNew):
                self.logger.error(f"Required files not found - cache: {cache}, pcapNew: {pcapNew}")
                return False

            # 执行tcpreplay命令
            tcpreplay = f'tcpreplay --loop=1 -c {shlex.quote(cache)} -i {interface1} -I {interface2} {shlex.quote(pcapNew)}'
            try:
                r = subprocess.getstatusoutput(tcpreplay)
                errorMsg=r[1].replace("\n", " ")
                if r[0] != 0:
                    
                    self.logger.error(f'tcpreplay failed with exit code {r[0]}, result: {errorMsg}')
                    return False
                else:
                    self.logger.info(f'Packet replay successful, result: {errorMsg}')
            except Exception as e:
                self.logger.error(f"Error executing tcpreplay command: {str(e)}")
                return False
            finally:

                # 清理临时文件
                cleanup_success = True
                if os.path.exists(cache):
                    try:
                        os.remove(cache)
                        self.logger.info(f"Deleted temporary cache file: {cache}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete cache file {cache}: {str(e)}")
                        cleanup_success = False

                if os.path.exists(pcapNew):
                    try:
                        os.remove(pcapNew)
                        self.logger.info(f"Deleted temporary pcap file: {pcapNew}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete pcap file {pcapNew}: {str(e)}")
                        cleanup_success = False

                return cleanup_success

        except Exception as e:
            self.logger.error(f"Error in pcapReplay.replay: {str(e)}")
            return False

    def _generate_ip_address(self, base_ip, increment):
        """
        生成IP地址，支持正确的自增长

        Args:
            base_ip (str): 基础IP地址，如 '*******'
            increment (int): 增长数值

        Returns:
            str: 生成的IP地址

        Examples:
            _generate_ip_address('*******', 0) -> '*******'
            _generate_ip_address('*******', 255) -> '*********'
            _generate_ip_address('*******', 256) -> '*******'
            _generate_ip_address('*******', 65536) -> '*******'
        """
        try:
            # 将IP地址转换为整数
            parts = base_ip.split('.')
            if len(parts) != 4:
                raise ValueError(f"Invalid IP address format: {base_ip}")

            # 验证每个部分都是有效的0-255范围
            for part in parts:
                if not part.isdigit() or not (0 <= int(part) <= 255):
                    raise ValueError(f"Invalid IP address part: {part}")

            # 将IP地址转换为32位整数
            ip_int = (int(parts[0]) << 24) + (int(parts[1]) << 16) + (int(parts[2]) << 8) + int(parts[3])

            # 加上增长值
            new_ip_int = ip_int + increment

            # 确保不超过32位整数的最大值（避免溢出）
            if new_ip_int > 0xFFFFFFFF:
                new_ip_int = 0xFFFFFFFF

            # 将整数转换回IP地址
            new_parts = [
                (new_ip_int >> 24) & 0xFF,
                (new_ip_int >> 16) & 0xFF,
                (new_ip_int >> 8) & 0xFF,
                new_ip_int & 0xFF
            ]

            return '.'.join(map(str, new_parts))

        except Exception as e:
            self.logger.error(f"Error generating IP address from {base_ip} with increment {increment}: {str(e)}")
            # 如果出错，返回基础IP地址
            return base_ip

    def _validate_pcap_format(self, pcap_path):
        """
        验证pcap文件格式的基本有效性
        """
        try:
            with open(pcap_path, 'rb') as f:
                # 读取文件头部
                header = f.read(24)

                if len(header) < 24:
                    self.logger.warning(f"Pcap file too small: {pcap_path}")
                    return False

                # 检查pcap文件魔数
                magic_numbers = [
                    b'\xd4\xc3\xb2\xa1',  # 标准pcap (little endian)
                    b'\xa1\xb2\xc3\xd4',  # 标准pcap (big endian)
                    b'\x0a\x0d\x0d\x0a',  # pcapng
                    b'\x4d\x3c\xb2\xa1',  # 修改的pcap
                    b'\xa1\xb2\x3c\x4d'   # 修改的pcap (big endian)
                ]

                file_magic = header[:4]
                if file_magic not in magic_numbers:
                    self.logger.warning(f"Invalid pcap magic number in file: {pcap_path}")
                    return False

                # 对于pcap文件，尝试读取第一个数据包头部
                if file_magic in [b'\xd4\xc3\xb2\xa1', b'\xa1\xb2\xc3\xd4']:
                    # 跳过全局头部，尝试读取第一个数据包记录头部
                    packet_header = f.read(16)
                    if len(packet_header) < 16:
                        self.logger.warning(f"No packet data found in pcap file: {pcap_path}")
                        return False

                    # 解析数据包长度
                    if file_magic == b'\xd4\xc3\xb2\xa1':  # little endian
                        import struct
                        _, _, caplen, _ = struct.unpack('<IIII', packet_header)
                    else:  # big endian
                        import struct
                        _, _, caplen, _ = struct.unpack('>IIII', packet_header)

                    # 检查数据包长度是否合理
                    if caplen == 0 or caplen > 65535:
                        self.logger.warning(f"Invalid packet length ({caplen}) in pcap file: {pcap_path}")
                        return False

                self.logger.info(f"Pcap file format validation passed: {pcap_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error validating pcap format for {pcap_path}: {str(e)}")
            return False
