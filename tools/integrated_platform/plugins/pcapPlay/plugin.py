#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报文回放插件

该插件提供网络报文（PCAP文件）的回放功能，支持多种回放模式和参数配置。
包含配置设置、任务管理、结果查看和日志监控等完整功能模块。

主要功能：
1. 网络接口配置和回放参数设置
2. 测试任务创建和PCAP文件上传
3. 任务执行状态监控和结果查看
4. 运行日志查看和管理

作者: 插件开发者
版本: 1.0.0
"""

from typing import Dict, List, Any
from flask import render_template, request, jsonify
from core.base_plugin import BasePlugin
import os
import json
import uuid
import time
import csv
import shlex
import subprocess
import psutil
from datetime import datetime, timedelta
from time import sleep
from werkzeug.utils import secure_filename
import re
from HBB.device import getDevice
import threading
import logging
from concurrent.futures import ThreadPoolExecutor
import socket
import struct
import traceback




class signatureTransfer:
    """签名转换的占位符类"""
    def __init__(self, task_id=None):
        self.task_id = task_id

    def transfer(self, sigPath, protocol, sigIds):
        """转换签名的占位符方法"""
        _ = sigPath, protocol, sigIds  # 避免未使用参数警告
        return {}, {}

TASK_PENGDING='进行中'
TASK_FINISH='已完成'
TASK_FAIL='失败'
TASK_SUCCESS='成功'
TASK_UNKOWN='未知'

_thread_local = threading.local()

def set_task_id(task_id):
    """设置当前线程的taskId"""
    _thread_local.task_id = task_id

def get_task_id():
    """获取当前线程的taskId"""
    return getattr(_thread_local, 'task_id', None)

def clear_task_id():
    """清除当前线程的taskId"""
    if hasattr(_thread_local, 'task_id'):
        delattr(_thread_local, 'task_id')

class TaskIdFormatter(logging.Formatter):
    """自定义格式化器，支持taskId，与插件统一格式兼容"""

    def __init__(self, base_format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'):
        """
        初始化格式化器

        Args:
            base_format: 基础日志格式
        """
        super().__init__(base_format, datefmt='%Y-%m-%d %H:%M:%S')

    def format(self, record):
        # 获取当前线程的taskId
        task_id = get_task_id()

        # 检查消息是否已经包含TaskID前缀，避免重复添加
        original_message = record.getMessage()
        if not original_message.startswith('[TaskID:'):
            if task_id:
                # 如果有taskId，在消息前添加TaskID信息
                record.msg = f"[TaskID: {task_id}] {original_message}"
            else:
                # 如果没有taskId，在消息前添加未知TaskID信息
                record.msg = f"{original_message}"
            record.args = None  # 清除args，避免重复格式化

        return super().format(record)

def _has_taskid_support(target_logger):
    """
    检查logger是否已经有TaskID支持

    Args:
        target_logger: 目标logger实例

    Returns:
        bool: 如果已经有TaskID支持返回True，否则返回False
    """
    # 检查所有处理器是否都有TaskID支持
    if not target_logger.handlers:
        return False

    for handler in target_logger.handlers:


        # 检查格式字符串是否包含TaskID
        if handler.formatter:
            format_str = getattr(handler.formatter, '_fmt', '')
            if 'TaskID' in format_str or '[TaskID:' in format_str:
                continue

        # 如果有任何一个处理器没有TaskID支持，返回False
        return False

    return True

def _add_taskid_support_to_logger(target_logger):
    """
    为现有logger添加TaskID支持，避免重复添加

    Args:
        target_logger: 目标logger实例
    """
    if _has_taskid_support(target_logger):
        return
    # 为所有处理器添加TaskID格式化器
    for handler in target_logger.handlers:
        


        # 添加TaskID格式化器
        current_formatter = handler.formatter
        original_format = getattr(current_formatter, '_fmt', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        new_formatter = TaskIdFormatter(original_format)
        handler.setFormatter(new_formatter)



class Plugin(BasePlugin):
    """
    报文回放插件主类

    继承自BasePlugin，提供PCAP文件的回放功能。
    支持配置管理、任务创建、状态监控和日志查看等功能。
    """

    def __init__(self, **kwargs):
        """
        初始化插件实例

        Args:
            **kwargs: 插件初始化参数，传递给父类BasePlugin

        功能：
        - 设置插件基本信息（名称、版本、作者等）
        - 创建必要的工作目录（uploads、tasks、logs）
        - 初始化配置文件和数据存储
        """
        super().__init__(**kwargs)

        # 设置插件基本信息
        self.name = "pcapPlay"
        self.displayName = "报文回放工具"
        self.description = "用于网络报文回放测试的工具插件，支持多种回放模式和参数配置"
        self.version = "1.0.0"
        self.author = "LiaoJunBo"
        # 创建工作目录
        self.pluginDir = os.path.dirname(__file__)
        self.uploadDir = os.path.join(self.pluginDir, 'uploads')
        self.taskDir = os.path.join(self.pluginDir, 'tasks')
        self.logDir = os.path.join(self.pluginDir, 'logs')

        # 设备日志目录（syslog）
        self.syslogDir = os.path.join(self.logDir, 'syslog')

        # 确保目录存在
        for directory in [self.uploadDir, self.taskDir, self.logDir, self.syslogDir]:
            os.makedirs(directory, exist_ok=True)

        # 初始化统一的日志配置
        self.logger = self._setup_plugin_logger()
        _add_taskid_support_to_logger(self.logger)

        # 将统一的logger传递给play模块
        from .play import init_logger
        init_logger(self.logger)

        # 配置文件路径
        self.configFile = os.path.join(self.pluginDir, 'config.json')

        # 初始化默认配置
        self.defaultConfig = {
            'device': {
                'ip': '',
                'username': '',
                'password': '',
                'saveCredentials': False
            },
            'replay': {
                'mode': 'tap',
                'interface1': '',
                'interface2': ''
            }
        }

        # 加载配置
        self.loadConfig()

    def loadConfig(self):
        """
        加载插件配置

        功能：
        - 从配置文件加载设置
        - 如果配置文件不存在，创建默认配置
        - 验证配置的完整性
        """
        try:
            if os.path.exists(self.configFile):
                with open(self.configFile, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)

                # 配置格式迁移：从 selectedInterfaces 格式迁移到 interface1/interface2 格式
                if 'replay' in self.config and 'selectedInterfaces' in self.config['replay']:
                    selectedInterfaces = self.config['replay']['selectedInterfaces']
                    self.config['replay']['interface1'] = selectedInterfaces.get('interface1', '')
                    self.config['replay']['interface2'] = selectedInterfaces.get('interface2', '')
                    # 删除旧格式
                    del self.config['replay']['selectedInterfaces']
                    # 保存迁移后的配置
                    self.saveConfig()
                    self.logger.info("配置格式已迁移到新版本")

                # 合并默认配置，确保所有必需的键都存在
                for key, value in self.defaultConfig.items():
                    if key not in self.config:
                        self.config[key] = value
                    elif isinstance(value, dict):
                        for subkey, subvalue in value.items():
                            if subkey not in self.config[key]:
                                self.config[key][subkey] = subvalue
            else:
                self.config = self.defaultConfig.copy()
                self.saveConfig()
        except Exception as e:
            self.logger.error(f"加载配置失败: {str(e)}")
            self.config = self.defaultConfig.copy()

    def saveConfig(self):
        """
        保存插件配置

        功能：
        - 将当前配置保存到配置文件
        - 确保配置格式正确
        """
        try:
            with open(self.configFile, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存配置失败: {str(e)}")

    def _setup_plugin_logger(self):
        """
        设置插件统一的日志配置

        Returns:
            logging.Logger: 配置好的日志记录器

        功能：
        - 创建插件专用的日志记录器
        - 统一日志格式和存储位置
        - 支持TaskID追踪功能
        """
        import logging.handlers
        from datetime import datetime

        # 创建插件专用logger
        logger_name = f"plugin.{self.name}"
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)

        # 清除现有处理器，避免重复
        if logger.handlers:
            logger.handlers.clear()


        # 禁用向根日志记录器传播，避免重复记录
        logger.propagate = False

        # 创建统一的日志格式化器
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        formatter = logging.Formatter(log_format, datefmt='%Y-%m-%d %H:%M:%S')

        # 创建文件处理器 - 主日志文件
        main_log_file = os.path.join(self.logDir, 'plugin.log')
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # 创建错误日志处理器
        error_log_file = os.path.join(self.logDir, 'error.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)

        #logger.info(f"插件日志系统初始化完成 - 日志目录: {self.logDir}")
        return logger



    def getRoutes(self) -> List[Dict[str, Any]]:
        """
        获取页面路由配置

        Returns:
            List[Dict[str, Any]]: 页面路由配置列表，包含主页面路由

        功能：
        - 配置插件的主页面路由
        - 设置路由规则、视图函数和HTTP方法
        """
        return [
            {
                "rule": "/",
                "view_func": self.indexPage,
                "methods": ["GET"],
                "endpoint": "index"
            },
            {
                "rule": "/create-task",
                "view_func": self.createTaskPage,
                "methods": ["GET"],
                "endpoint": "create_task_page"
            },
            {
                "rule": "/task-results",
                "view_func": self.taskResultsPage,
                "methods": ["GET"],
                "endpoint": "task_results_page"
            },
            {
                "rule": "/runtime-logs",
                "view_func": self.runtimeLogsPage,
                "methods": ["GET"],
                "endpoint": "runtime_logs_page"
            }
        ]

    def getApiRoutes(self) -> List[Dict[str, Any]]:
        """
        获取API路由配置

        Returns:
            List[Dict[str, Any]]: API路由配置列表，包含所有REST API接口

        功能：
        - 配置配置管理相关API接口
        - 配置任务管理相关API接口
        - 配置日志查询相关API接口
        """
        return [
            # 配置管理API
            {
                "rule": "/config",
                "view_func": self.getConfig,
                "methods": ["GET"],
                "endpoint": "get_config"
            },
            {
                "rule": "/config",
                "view_func": self.updateConfig,
                "methods": ["POST"],
                "endpoint": "update_config"
            },
            {
                "rule": "/config/interfaces",
                "view_func": self.getNetworkInterfaces,
                "methods": ["GET"],
                "endpoint": "get_interfaces"
            },


            # 任务管理API
            {
                "rule": "/tasks",
                "view_func": self.getTasks,
                "methods": ["GET"],
                "endpoint": "pcap_get_tasks_list"
            },
            {
                "rule": "/tasks",
                "view_func": self.createTask,
                "methods": ["POST"],
                "endpoint": "create_task"
            },
            {
                "rule": "/tasks/<task_id>/delete",
                "view_func": self.deleteTask,
                "methods": ["DELETE"],
                "endpoint": "delete_task"
            },



            # 日志查询API
            {
                "rule": "/logs",
                "view_func": self.getLogs,
                "methods": ["GET"],
                "endpoint": "get_logs"
            },
            {
                "rule": "/logs/<task_id>",
                "view_func": self.getTaskLogs,
                "methods": ["GET"],
                "endpoint": "get_task_logs"
            },

            # 报告下载API
            {
                "rule": "/reports/<task_id>/download",
                "view_func": self.downloadReport,
                "methods": ["GET"],
                "endpoint": "download_report"
            }
        ]

    def getNavItems(self) -> List[Dict[str, Any]]:
        """
        获取导航项配置

        Returns:
            List[Dict[str, Any]]: 导航项配置列表，包含插件在主导航中的显示

        功能：
        - 配置插件在主导航菜单中的显示
        - 设置图标、标题和排序位置
        """
        return [
            {
                "title": "报文回放",
                "url": "/plugins/pcapPlay/",
                "icon": "fas fa-play-circle",
                "order": 30
            }
        ]

    def onInitialize(self):
        """
        插件初始化回调方法

        功能：
        - 在插件加载完成后执行
        - 记录初始化完成日志
        - 执行必要的初始化操作
        """
        self.logger.info("报文回放插件初始化完成")

        # 清理过期的任务和日志文件
        self.cleanupOldFiles()

    # ==================== 页面视图函数 ====================

    def indexPage(self):
        """
        插件主页面视图

        Returns:
            str: 渲染后的HTML页面内容
            tuple: 错误情况下返回(错误信息, HTTP状态码)

        功能：
        - 渲染插件的主要操作界面
        - 提供4个功能模块的页签界面
        - 传递插件信息和配置到模板中
        """
        try:
            return render_template('pcapReplay/index.html',
                                 pluginInfo=self.getInfo(),
                                 config=self.config)
        except Exception as e:
            self.logger.error(f"加载报文回放插件页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def createTaskPage(self):
        """
        新建测试任务页面视图

        Returns:
            str: 渲染后的HTML页面内容
            tuple: 错误情况下返回(错误信息, HTTP状态码)

        功能：
        - 渲染新建测试任务的界面
        - 提供任务名称输入和文件上传功能
        - 传递插件信息到模板中
        """
        try:
            return render_template('pcapReplay/create_task.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载新建测试任务页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def taskResultsPage(self):
        """
        任务结果查询页面视图

        Returns:
            str: 渲染后的HTML页面内容
            tuple: 错误情况下返回(错误信息, HTTP状态码)

        功能：
        - 渲染任务结果查询界面
        - 显示所有任务的列表信息
        - 支持报告下载功能
        - 传递插件信息到模板中
        """
        try:
            return render_template('pcapReplay/task_results.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载任务结果查询页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def runtimeLogsPage(self):
        """
        运行日志页面视图

        Returns:
            str: 渲染后的HTML页面内容
            tuple: 错误情况下返回(错误信息, HTTP状态码)

        功能：
        - 渲染运行日志查看界面
        - 显示最新的500条运行日志
        - 支持5秒自动刷新功能
        - 传递插件信息到模板中
        """
        try:
            return render_template('pcapReplay/runtime_logs.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载运行日志页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    # ==================== 配置管理API ====================

    def getConfig(self):
        """
        获取插件配置API

        Returns:
            Response: JSON格式的配置数据

        功能：
        - 返回当前插件的所有配置信息
        - 包括网络接口、回放参数、系统设置等
        """
        try:
            
            return jsonify({
                'success': True,
                'data': self.config
            })
        except Exception as e:
            self.logger.error(f"获取配置失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取配置失败: {str(e)}"
            }), 500

    def updateConfig(self):
        """
        更新插件配置API

        Returns:
            Response: JSON格式的操作结果

        功能：
        - 接收前端提交的配置更新
        - 验证配置数据的有效性
        - 保存配置到文件
        """
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '无效的请求数据'
                }), 400

            # 验证配置数据
            if self.validateConfig(data):
                # 更新配置
                self.config.update(data)
                self.saveConfig()

                return jsonify({
                    'success': True,
                    'message': '配置更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '配置数据验证失败'
                }), 400

        except Exception as e:
            self.logger.error(f"更新配置失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"更新配置失败: {str(e)}"
            }), 500

    def getNetworkInterfaces(self):
        """
        获取网络接口列表API

        Returns:
            Response: JSON格式的网络接口列表

        功能：
        - 获取系统可用的网络接口
        - 返回接口名称和基本信息
        """
        try:
            interfaces = list(psutil.net_if_addrs().keys())
            self.logger.info(f"可用网络接口: {interfaces}")

            return jsonify({
                "success": True,
                "interfaces": interfaces
            })

        except Exception as e:
            self.logger.error(f"获取网络接口失败: {str(e)}")
            return jsonify({
                "success": False,
                "error": f"获取网络接口失败: {str(e)}"
            }), 500



    # ==================== 任务管理API ====================

    def getTasks(self):
        """
        获取任务列表API

        Returns:
            Response: JSON格式的任务列表

        功能：
        - 获取所有测试任务的列表
        - 支持分页和过滤参数
        - 返回任务基本信息和状态
        """
        try:
            # 获取查询参数
            page = request.args.get('page', 1, type=int)
            pageSize = request.args.get('pageSize', 10, type=int)
            status = request.args.get('status', '')

            # 读取任务数据
            tasks = self.loadTasks()

            # 过滤任务
            if status:
                tasks = [task for task in tasks if task.get('status') == status]

            # 分页处理
            total = len(tasks)
            start = (page - 1) * pageSize
            end = start + pageSize
            pagedTasks = tasks[start:end]

            return jsonify({
                'success': True,
                'data': {
                    'tasks': pagedTasks,
                    'total': total,
                    'page': page,
                    'pageSize': pageSize
                }
            })
        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务列表失败: {str(e)}"
            }), 500

    def getTasks(self):
        """
        获取任务列表API

        Returns:
            Response: JSON格式的任务列表数据

        功能：
        - 获取所有任务的列表信息
        - 返回任务的基本信息（ID、名称、状态、结果、创建时间等）
        - 支持前端任务结果页面的数据展示
        """
        try:
            # 获取所有任务文件
            tasks_dir = self.taskDir
            if not os.path.exists(tasks_dir):
                os.makedirs(tasks_dir, exist_ok=True)

            task_list = []

            # 遍历任务目录中的所有任务文件
            for filename in os.listdir(tasks_dir):
                if filename.endswith('.json'):
                    task_id = filename[:-5]  # 移除 .json 扩展名
                    task_file_path = os.path.join(tasks_dir, filename)

                    try:
                        with open(task_file_path, 'r', encoding='utf-8') as f:
                            task_data = json.load(f)

                        # 构建任务信息
                        task_info = {
                            'id': task_data.get('id', task_id),
                            'name': task_data.get('name', '未知任务'),
                            'status': self._getTaskStatusText(task_data.get('status', 0)),
                            'result': self._getTaskResultText(task_data.get('result', 0)),
                            'createTime': task_data.get('createTime', ''),
                            'reportPath': task_data.get('reportPath', ''),
                            'hasReport': bool(task_data.get('reportPath', ''))
                        }

                        task_list.append(task_info)

                    except Exception as e:
                        self.logger.error(f"读取任务文件失败 {filename}: {str(e)}")
                        continue

            # 按创建时间倒序排列
            task_list.sort(key=lambda x: x.get('createTime', ''), reverse=True)

            self.logger.info(f"获取任务列表成功，共 {len(task_list)} 个任务")

            return jsonify({
                'success': True,
                'data': {
                    'tasks': task_list,
                    'total': len(task_list)
                },
                'message': '获取任务列表成功'
            })

        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务列表失败: {str(e)}"
            }), 500

    def createTask(self):
        """
        创建新任务API（包含文件上传）

        Returns:
            Response: JSON格式的创建结果

        功能：
        - 接收任务创建请求和文件上传
        - 验证任务参数和文件
        - 保存上传的文件
        - 创建任务记录并保存
        """
        try:
            # 检查请求是否有效
            self.logger.debug(f"请求内容类型: {request.content_type}")
            self.logger.debug(f"请求方法: {request.method}")
            self.logger.debug(f"表单数据: {dict(request.form)}")
            self.logger.debug(f"文件数据: {list(request.files.keys())}")

            if not request.form and not request.files:
                self.logger.error("请求中没有表单数据或文件数据")
                return jsonify({
                    'success': False,
                    'message': '请求格式错误：缺少表单数据'
                }), 400

            # 获取表单数据
            taskName = request.form.get('taskName') or request.form.get('name')
            if not taskName:
                return jsonify({
                    'success': False,
                    'message': '请输入任务名称'
                }), 400

            # 检查是否有上传的文件
            if 'files' not in request.files:
                return jsonify({
                    'success': False,
                    'message': '请选择要上传的报文文件'
                }), 400

            files = request.files.getlist('files')
            if not files or all(f.filename == '' for f in files):
                return jsonify({
                    'success': False,
                    'message': '请选择要上传的报文文件'
                }), 400

            # 过滤有效的报文文件
            validFiles, invalidCount = self.filterPcapFiles(files)

            if not validFiles:
                return jsonify({
                    'success': False,
                    'message': '没有找到有效的报文文件，请上传.pcap、.pcapng或.cap文件'
                }), 400

            # 处理文件上传
            uploadedFiles = []
            timestamp = int(time.time())

            for file in validFiles:
                # 生成安全的文件名
                filename = secure_filename(file.filename)
                # 为避免文件名冲突，添加时间戳和随机数
                import random
                random_suffix = random.randint(1000, 9999)
                filename = f"{timestamp}_{random_suffix}_{filename}"

                # 保存文件
                filepath = os.path.join(self.uploadDir, filename)
                file.save(filepath)

                # 获取文件信息
                fileSize = os.path.getsize(filepath)
                uploadedFiles.append({
                    'filename': filename,
                    'originalName': file.filename,
                    'size': fileSize,
                    'path': filepath
                })

                self.logger.info(f"文件保存成功: {filepath}")

            # 记录文件处理结果
            if invalidCount > 0:
                self.logger.info(f"成功处理 {len(uploadedFiles)} 个报文文件，跳过 {invalidCount} 个非报文文件")

            # 创建任务
            global TASK_PENGDING,TASK_UNKOWN
            taskId = str(uuid.uuid4())
            currentTime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            task = {
                'id': taskId,
                'name': taskName,
                'status': TASK_PENGDING,
                'result': TASK_UNKOWN,
                'reportPath': '',
                'createTime': currentTime,
                'updateTime': currentTime
            }

            # 保存任务
            self.saveTask(task)

            # 提取文件路径列表
            pcapPaths = [file_info['path'] for file_info in uploadedFiles]

            # 在后台线程中执行测试
            def runTestInBackground():
                try:
                    self.mainTest(taskId=taskId, taskName=taskName, pcapPaths=pcapPaths)
                except Exception as e:
                    self.logger.error(f"Background test execution failed: {str(e)}")

            # 启动后台线程
            test_thread = threading.Thread(target=runTestInBackground)
            test_thread.daemon = True
            test_thread.start()

            self.logger.info(f"任务创建成功: {taskId}, 文件数量: {len(uploadedFiles)}")

            # 构建成功消息
            success_message = f'任务创建成功，正在后台执行测试（共 {len(uploadedFiles)} 个报文文件）'
            if invalidCount > 0:
                success_message += f'，已跳过 {invalidCount} 个非报文文件'

            return jsonify({
                'success': True,
                'data': task,
                'message': success_message,
                'fileStats': {
                    'validFiles': len(uploadedFiles),
                    'invalidFiles': invalidCount,
                    'totalFiles': len(files)
                }
            })

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"创建任务失败: {error_msg}")

            # 根据错误类型返回不同的状态码
            if "400 Bad Request" in error_msg or "Bad Request" in error_msg:
                return jsonify({
                    'success': False,
                    'message': '请求格式错误，请检查上传的文件格式和大小'
                }), 400
            elif "413" in error_msg or "Request Entity Too Large" in error_msg:
                return jsonify({
                    'success': False,
                    'message': '上传文件过大，请确保文件小于16MB'
                }), 413
            else:
                return jsonify({
                    'success': False,
                    'message': f"创建任务失败: {error_msg}"
                }), 500



    def updateTask(self, taskId,status,result,reportPath):
        """
        更新任务API

        Args:
            taskId (str): 任务ID

        Returns:
            Response: JSON格式的操作结果

        功能：
        - 更新指定的任务的状态
        """
        oldTask = self.loadTask(taskId)
        if not oldTask:
            self.logger.error(f"任务不存在: {taskId}")
            return

        taskName = oldTask['name']
        currentTime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 保留原有的创建时间，更新其他字段
        task = {
            'id': taskId,
            'name': taskName,
            'status': status,
            'result': result,
            'reportPath': reportPath,
            'createTime': oldTask.get('createTime', currentTime),  # 保留原创建时间
            'updateTime': currentTime  # 更新修改时间
        }
        self.saveTask(task)

        return

    def deleteTask(self, task_id):
        """
        删除任务API

        Args:
            task_id (str): 任务ID

        Returns:
            Response: JSON格式的操作结果

        功能：
        - 删除指定的任务
        - 清理相关的文件和日志
        """
        try:
            task = self.loadTask(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'message': '任务不存在'
                }), 404

            # 允许删除正在进行中的任务
            # if task['status'] == '进行中':
            #     return jsonify({
            #         'success': False,
            #         'message': '无法删除正在运行的任务'
            #     }), 400

            # 删除任务文件
            taskFile = os.path.join(self.taskDir, f"{task_id}.json")
            if os.path.exists(taskFile):
                os.remove(taskFile)
                self.logger.info(f"删除任务文件: {taskFile}")

            # 删除相关的上传文件（按时间戳模式匹配）
            try:
                import glob
                # 查找以任务ID相关的上传文件
                uploadPattern = os.path.join(self.uploadDir, f"*_{task_id}_*")
                uploadFiles = glob.glob(uploadPattern)

                # 也查找可能的时间戳文件
                for filename in os.listdir(self.uploadDir):
                    if task_id in filename:
                        uploadFiles.append(os.path.join(self.uploadDir, filename))

                # 删除找到的文件
                for uploadFile in set(uploadFiles):  # 使用set去重
                    if os.path.exists(uploadFile):
                        os.remove(uploadFile)
                        self.logger.info(f"删除上传文件: {uploadFile}")

            except Exception as e:
                self.logger.warning(f"删除上传文件时出错: {str(e)}")

            # 删除报告文件
            if task.get('reportPath') and os.path.exists(task['reportPath']):
                try:
                    os.remove(task['reportPath'])
                    self.logger.info(f"删除报告文件: {task['reportPath']}")
                except Exception as e:
                    self.logger.warning(f"删除报告文件时出错: {str(e)}")

            # 删除可能的日志文件
            try:
                logPattern = os.path.join(self.logDir, f"*{task_id}*")
                logFiles = glob.glob(logPattern)
                for logFile in logFiles:
                    if os.path.exists(logFile):
                        os.remove(logFile)
                        self.logger.info(f"删除日志文件: {logFile}")
            except Exception as e:
                self.logger.warning(f"删除日志文件时出错: {str(e)}")

            return jsonify({
                'success': True,
                'message': '任务删除成功'
            })

        except Exception as e:
            self.logger.error(f"删除任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"删除任务失败: {str(e)}"
            }), 500



    # ==================== 日志查询API ====================

    def getLogs(self):
        """
        获取系统日志API

        Returns:
            Response: JSON格式的日志数据

        功能：
        - 获取插件的系统日志
        - 支持分页和过滤
        """
        try:
            # 获取查询参数
            page = request.args.get('page', 1, type=int)
            pageSize = request.args.get('pageSize', 50, type=int)
            level = request.args.get('level', '')
            taskId = request.args.get('taskId', '')  # 改为任务ID过滤

            # 读取日志文件
            logs = self.loadSystemLogs()

            # 过滤日志
            if level:
                logs = [log for log in logs if log.get('level') == level]

            if taskId:
                # 任务ID过滤
                logs = [log for log in logs if log.get('source') == taskId]

            # 分页处理
            total = len(logs)
            start = (page - 1) * pageSize
            end = start + pageSize
            pagedLogs = logs[start:end]

            return jsonify({
                'success': True,
                'data': {
                    'logs': pagedLogs,
                    'total': total,
                    'page': page,
                    'pageSize': pageSize
                }
            })
        except Exception as e:
            self.logger.error(f"获取日志失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取日志失败: {str(e)}"
            }), 500

    def getTaskLogs(self, taskId):
        """
        获取任务日志API

        Args:
            taskId (str): 任务ID

        Returns:
            Response: JSON格式的任务日志

        功能：
        - 获取指定任务的执行日志
        - 包括回放过程中的详细信息
        """
        try:
            # 检查任务是否存在
            task = self.loadTask(taskId)
            if not task:
                return jsonify({
                    'success': False,
                    'message': '任务不存在'
                }), 404

            # 读取任务日志
            logs = self.loadTaskLogs(taskId)

            return jsonify({
                'success': True,
                'data': logs
            })
        except Exception as e:
            self.logger.error(f"获取任务日志失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务日志失败: {str(e)}"
            }), 500

    def downloadReport(self, task_id):
        """
        下载任务报告API

        Args:
            task_id (str): 任务ID

        Returns:
            Response: 文件下载响应或错误信息

        功能：
        - 检查任务是否存在
        - 检查报告文件是否存在
        - 提供报告文件下载
        """
        try:
            from flask import send_file, abort

            # 检查任务是否存在
            task = self.loadTask(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'message': '任务不存在'
                }), 404

            # 检查任务是否有报告
            report_path = task.get('reportPath', '')
            if not report_path:
                return jsonify({
                    'success': False,
                    'message': '该任务没有生成报告'
                }), 404

            # 检查报告文件是否存在
            if not os.path.exists(report_path):
                return jsonify({
                    'success': False,
                    'message': '报告文件不存在'
                }), 404

            # 生成下载文件名
            task_name = task.get('name', 'unknown')
            file_extension = os.path.splitext(report_path)[1]
            download_filename = f"{task_name}_{task_id}_report{file_extension}"

            # 发送文件
            return send_file(
                report_path,
                as_attachment=True,
                download_name=download_filename,
                mimetype='application/octet-stream'
            )

        except Exception as e:
            self.logger.error(f"下载报告失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"下载报告失败: {str(e)}"
            }), 500

    # ==================== 辅助方法 ====================

    def _getTaskStatusText(self, status):
        """
        获取任务状态的显示文本

        Args:
            status: 任务状态（字符串或数字）

        Returns:
            str: 状态文本描述
        """
        # 如果已经是字符串，直接返回
        if isinstance(status, str):
            return status

        # 如果是数字，进行转换
        status_map = {
            0: TASK_PENGDING,
            1: TASK_FINISH,
            2: '已取消'
        }
        return status_map.get(status, '未知')

    def _getTaskResultText(self, result):
        """
        获取任务结果的显示文本

        Args:
            result: 任务结果（字符串或数字）

        Returns:
            str: 结果文本描述
        """
        # 如果已经是字符串，直接返回
        if isinstance(result, str):
            return result

        # 如果是数字，进行转换
        result_map = {
            0: TASK_UNKOWN,
            1: TASK_SUCCESS,
            2: TASK_FAIL
        }
        return result_map.get(result, TASK_UNKOWN)

    def validateConfig(self, config):
        """
        验证配置数据

        Args:
            config (dict): 配置数据

        Returns:
            bool: 验证结果

        功能：
        - 验证配置数据的格式和有效性
        - 检查必需字段和数据类型
        """
        try:
            # 验证设备配置
            if 'device' in config:
                device = config['device']
                if not isinstance(device, dict):
                    return False

                # 验证必需字段
                required_fields = ['ip', 'username', 'password', 'saveCredentials']
                for field in required_fields:
                    if field not in device:
                        return False

                # 验证数据类型
                if not isinstance(device['ip'], str):
                    return False
                if not isinstance(device['username'], str):
                    return False
                if not isinstance(device['password'], str):
                    return False
                if not isinstance(device['saveCredentials'], bool):
                    return False

            # 验证回放配置
            if 'replay' in config:
                replay = config['replay']
                if not isinstance(replay, dict):
                    return False

                # 验证必需字段
                required_fields = ['mode', 'interface1', 'interface2']
                for field in required_fields:
                    if field not in replay:
                        return False

                # 验证数据类型
                if not isinstance(replay['mode'], str):
                    return False
                if not isinstance(replay['interface1'], str):
                    return False
                if not isinstance(replay['interface2'], str):
                    return False

                # 验证模式值
                if replay['mode'] not in ['tap', 'bridge']:
                    return False

            return True
        except Exception as e:
            self.logger.error(f"配置验证失败: {str(e)}")
            return False

    def allowedFile(self, filename):
        """
        检查文件是否允许上传

        Args:
            filename (str): 文件名

        Returns:
            bool: 是否允许上传

        功能：
        - 检查文件扩展名是否为支持的格式
        - 支持.pcap、.pcapng和.cap文件
        """
        allowedExtensions = {'pcap', 'pcapng', 'cap'}
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in allowedExtensions

    def filterPcapFiles(self, files):
        """
        从文件列表中过滤出报文文件

        Args:
            files (list): 文件列表

        Returns:
            tuple: (有效文件列表, 无效文件数量)

        功能：
        - 过滤出支持的报文文件格式
        - 统计有效和无效文件数量
        - 用于文件夹上传时的文件筛选
        """
        validFiles = []
        invalidCount = 0

        for file in files:
            if file.filename and self.allowedFile(file.filename):
                validFiles.append(file)
            else:
                invalidCount += 1

        return validFiles, invalidCount

    def loadTasks(self):
        """
        加载所有任务

        Returns:
            list: 任务列表

        功能：
        - 从任务目录读取所有任务文件
        - 返回任务列表，按创建时间排序
        """
        tasks = []
        try:
            for filename in os.listdir(self.taskDir):
                if filename.endswith('.json'):
                    taskFile = os.path.join(self.taskDir, filename)
                    with open(taskFile, 'r', encoding='utf-8') as f:
                        task = json.load(f)
                        tasks.append(task)

            # 按创建时间排序
            tasks.sort(key=lambda x: x.get('createTime', ''), reverse=True)
        except Exception as e:
            self.logger.error(f"加载任务列表失败: {str(e)}")

        return tasks

    def loadTask(self, taskId):
        """
        加载单个任务

        Args:
            taskId (str): 任务ID

        Returns:
            dict: 任务数据，如果不存在返回None

        功能：
        - 根据任务ID加载任务详情
        """
        try:
            taskFile = os.path.join(self.taskDir, f"{taskId}.json")
            if os.path.exists(taskFile):
                with open(taskFile, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"加载任务失败: {str(e)}")

        return None

    def saveTask(self, task):
        """
        保存任务数据

        Args:
            task (dict): 任务数据

        功能：
        - 将任务数据保存到文件
        """
        try:
            taskFile = os.path.join(self.taskDir, f"{task['id']}.json")
            with open(taskFile, 'w', encoding='utf-8') as f:
                json.dump(task, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存任务失败: {str(e)}")

    def loadSystemLogs(self):
        """
        加载系统日志

        Returns:
            list: 日志列表

        功能：
        - 读取插件的统一日志文件
        - 返回格式化的日志数据
        - 获取最新的500条日志记录
        """
        logs = []
        try:
            # 读取统一的插件日志文件
            main_log_file = os.path.join(self.logDir, 'plugin.log')
            play_log_file = os.path.join(self.logDir, 'play.log')

            # 收集所有日志文件
            log_files = []
            if os.path.exists(main_log_file):
                log_files.append(main_log_file)
            if os.path.exists(play_log_file):
                log_files.append(play_log_file)

            # 如果没有统一日志文件，尝试读取旧的日志文件（向后兼容）
            if not log_files:
                legacy_log_file = os.path.join(self.logDir, 'legacy.log')
                if os.path.exists(legacy_log_file):
                    log_files.append(legacy_log_file)

            all_lines = []

            # 读取所有日志文件
            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        for line in lines:
                            line = line.strip()
                            if line:
                                all_lines.append((line, os.path.basename(log_file)))
                except Exception as e:
                    self.logger.warning(f"读取日志文件失败 {log_file}: {str(e)}")

            # 按时间排序（简单排序，基于行的时间戳）
            all_lines.sort(key=lambda x: x[0][:19] if len(x[0]) >= 19 else x[0])

            # 获取最新的500条日志
            recent_lines = all_lines[-500:] if len(all_lines) > 500 else all_lines

            for line, source_file in reversed(recent_lines):  # 倒序显示，最新的在前
                try:
                    # 解析统一日志格式: 2025-07-16 09:25:55 - plugin.pcapPlay - INFO - 消息内容
                    parts = line.split(' - ', 3)
                    if len(parts) >= 4:
                        timestamp = parts[0]
                        source = parts[1]
                        level = parts[2]
                        message = parts[3]

                        # 提取TaskID信息（如果存在）
                        task_id = None
                        if '[TaskID:' in message:
                            # 提取TaskID并清理消息
                            import re
                            taskid_match = re.search(r'\[TaskID:\s*([^\]]+)\]', message)
                            if taskid_match:
                                task_id = taskid_match.group(1).strip()
                                message = re.sub(r'\[TaskID:\s*[^\]]+\]\s*', '', message)

                        logs.append({
                            'time': timestamp,
                            'level': level,
                            'source': task_id,  # 来源改为任务ID，没有任务ID时为None
                            'message': message
                        })
                    else:
                        # 如果格式不匹配，作为普通消息处理
                        logs.append({
                            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'level': 'INFO',
                            'source': f'system({source_file})',
                            'message': line
                        })
                except Exception as parseError:
                    # 解析失败时的处理
                    logs.append({
                        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'level': 'WARN',
                        'source': f'parser({source_file})',
                        'message': f'日志解析失败: {line[:100]}...'
                    })

            if not logs:
                # 没有日志时返回提示信息
                logs.append({
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'level': 'INFO',
                    'source': 'system',
                    'message': '暂无日志记录或日志文件尚未生成'
                })

        except Exception as e:
            self.logger.error(f"加载系统日志失败: {str(e)}")
            logs.append({
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'level': 'ERROR',
                'source': 'system',
                'message': f'加载日志失败: {str(e)}'
            })

        return logs

    def loadTaskLogs(self, taskId):
        """
        加载任务日志

        Args:
            taskId (str): 任务ID

        Returns:
            list: 任务日志列表

        功能：
        - 读取指定任务的执行日志
        """
        logs = []
        try:
            logFile = os.path.join(self.logDir, f"{taskId}.log")
            if os.path.exists(logFile):
                with open(logFile, 'r', encoding='utf-8') as f:
                    for line in f:
                        # 解析日志行
                        logs.append({
                            'time': datetime.now().isoformat(),
                            'message': line.strip()
                        })
        except Exception as e:
            self.logger.error(f"加载任务日志失败: {str(e)}")

        return logs

    def cleanupOldFiles(self):
        """
        清理过期文件

        功能：
        - 清理过期的任务文件和日志
        - 根据配置的保留时间进行清理
        """
        try:
            retentionDays = self.config.get('systemSettings', {}).get('logRetentionDays', 30)
            cutoffTime = datetime.now() - timedelta(days=retentionDays)

            # 清理过期的任务文件
            for filename in os.listdir(self.taskDir):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.taskDir, filename)
                    fileTime = datetime.fromtimestamp(os.path.getmtime(filepath))
                    if fileTime < cutoffTime:
                        os.remove(filepath)
                        self.logger.info(f"清理过期任务文件: {filename}")

            # 清理过期的日志文件
            for filename in os.listdir(self.logDir):
                if filename.endswith('.log'):
                    filepath = os.path.join(self.logDir, filename)
                    fileTime = datetime.fromtimestamp(os.path.getmtime(filepath))
                    if fileTime < cutoffTime:
                        os.remove(filepath)
                        self.logger.info(f"清理过期日志文件: {filename}")

        except Exception as e:
            self.logger.error(f"清理过期文件失败: {str(e)}")

    def rewrite(self, **kwargs):
        try:
            # 参数验证
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']

            # 验证输入文件是否存在
            if not os.path.exists(pcapPath):
                raise FileNotFoundError(f"Input pcap file not found: {pcapPath}")

            dstMac = '00:1C:00:00:00:01'

            # 安全地处理文件路径
            if '.' not in pcapPath:
                self.logger.error(f"Invalid pcap file path format: {pcapPath}")
                return None, None

            pcapName = pcapPath[:pcapPath.rfind('.')]
            cache = pcapName + '.cache'
            outputfile = pcapName + '_out.pcap'
            global PATH
            fragConf=os.path.join(PATH, "uploads", "frag.conf") # 定义报文分片的配置文件，规定了tcprewrite时对报文分片的大小

            # 执行tcpprep命令
            tcpprep = f'tcpprep -a client -i {shlex.quote(pcapPath)} -o {shlex.quote(cache)}'
            try:
                r = subprocess.getstatusoutput(tcpprep)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcpprep command: {str(e)}")
                return None, None

            # 执行tcprewrite命令
            tcprewrite = f'tcprewrite --fragroute={fragConf} --enet-dmac={dstMac} --endpoints={srcIp}:{dstIp} -i {shlex.quote(pcapPath)} -c {shlex.quote(cache)} -o {shlex.quote(outputfile)}'
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None, None

            # 再次执行tcpprep命令，根据tcprewrite后输出的报文进行重新生成客户端和服务器区分的cache，避免报文分片后cache文件没有更新
            tcpprep = f'tcpprep -a client -i {shlex.quote(outputfile)} -o {shlex.quote(cache)}'
            try:
                r = subprocess.getstatusoutput(tcpprep)
                if r[0] != 0:
                    errorMsg=r[1].replace("\n"," ")
                    self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                    return None, None
            except Exception as e:
                self.logger.error(f"Error executing tcpprep command: {str(e)}")
                return None, None

            return cache, outputfile

        except Exception as e:
            self.logger.error(f"Error in pcapReplay.rewrite: {str(e)}")
            return None, None

    def replay(self, **kwargs):
        try:
            # 参数验证
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']
            interface1 = kwargs.get('interface1', '')
            interface2 = kwargs.get('interface2', interface1)

            # 验证网络接口参数
            if not interface1:
                self.logger.error("Network interface1 is required for packet replay")
                return False

            # 调用rewrite方法
            cache, pcapNew = self.rewrite(srcIp=srcIp, dstIp=dstIp, pcapPath=pcapPath)

            # 检查rewrite是否成功
            if cache is None or pcapNew is None:
                self.logger.error("Failed to rewrite pcap file, cannot proceed with replay")
                return False

            # 验证生成的文件是否存在
            if not os.path.exists(cache) or not os.path.exists(pcapNew):
                self.logger.error(f"Required files not found - cache: {cache}, pcapNew: {pcapNew}")
                return False

            # 执行tcpreplay命令
            tcpreplay = f'tcpreplay --loop=1 -c {shlex.quote(cache)} -i {interface1} -I {interface2} {shlex.quote(pcapNew)}'
            try:
                r = subprocess.getstatusoutput(tcpreplay)
                errorMsg=r[1].replace("\n", " ")
                if r[0] != 0:
                    
                    self.logger.error(f'tcpreplay failed with exit code {r[0]}, result: {errorMsg}')
                    return False
                else:
                    self.logger.info(f'Packet replay successful, result: {errorMsg}')
            except Exception as e:
                self.logger.error(f"Error executing tcpreplay command: {str(e)}")
                return False

            # 清理临时文件
            cleanup_success = True
            if os.path.exists(cache):
                try:
                    os.remove(cache)
                    self.logger.info(f"Deleted temporary cache file: {cache}")
                except Exception as e:
                    self.logger.error(f"Failed to delete cache file {cache}: {str(e)}")
                    cleanup_success = False

            if os.path.exists(pcapNew):
                try:
                    os.remove(pcapNew)
                    self.logger.info(f"Deleted temporary pcap file: {pcapNew}")
                except Exception as e:
                    self.logger.error(f"Failed to delete pcap file {pcapNew}: {str(e)}")
                    cleanup_success = False

            return cleanup_success

        except Exception as e:
            self.logger.error(f"Error in pcapReplay.replay: {str(e)}")
            return False
    
    def _generate_ip_address(self, base_ip, increment):
        """
        生成IP地址，支持正确的自增长

        Args:
            base_ip (str): 基础IP地址，如 '*******'
            increment (int): 增长数值

        Returns:
            str: 生成的IP地址

        Examples:
            _generate_ip_address('*******', 0) -> '*******'
            _generate_ip_address('*******', 255) -> '*********'
            _generate_ip_address('*******', 256) -> '*******'
            _generate_ip_address('*******', 65536) -> '*******'
        """
        try:
            # 将IP地址转换为整数
            parts = base_ip.split('.')
            if len(parts) != 4:
                raise ValueError(f"Invalid IP address format: {base_ip}")

            # 验证每个部分都是有效的0-255范围
            for part in parts:
                if not part.isdigit() or not (0 <= int(part) <= 255):
                    raise ValueError(f"Invalid IP address part: {part}")

            # 将IP地址转换为32位整数
            ip_int = (int(parts[0]) << 24) + (int(parts[1]) << 16) + (int(parts[2]) << 8) + int(parts[3])

            # 加上增长值
            new_ip_int = ip_int + increment

            # 确保不超过32位整数的最大值（避免溢出）
            if new_ip_int > 0xFFFFFFFF:
                new_ip_int = 0xFFFFFFFF

            # 将整数转换回IP地址
            new_parts = [
                (new_ip_int >> 24) & 0xFF,
                (new_ip_int >> 16) & 0xFF,
                (new_ip_int >> 8) & 0xFF,
                new_ip_int & 0xFF
            ]

            return '.'.join(map(str, new_parts))

        except Exception as e:
            self.logger.error(f"Error generating IP address from {base_ip} with increment {increment}: {str(e)}")
            # 如果出错，返回基础IP地址
            return base_ip

    # preTest 方法已移至 pcapTest 类中处理
    

    def mainTest(self, **kwargs):
        """
        主测试方法，使用 pcapTest 类执行报文回放测试

        Args:
            **kwargs: 包含 taskId, taskName, pcapPaths 等参数
        """
        taskId = kwargs['taskId']
        taskName = kwargs.get('taskName', '')
        pcapPaths = kwargs['pcapPaths']

        try:
            # 创建 pcapTest 实例，传递插件的 logger
            from .play import pcapTest
            pcap_test = pcapTest(
                taskId=taskId,
                taskName=taskName
            )

            # 执行主测试逻辑
            pcap_test.mainTest(
                taskId=taskId,
                taskName=taskName,
                pcapPaths=pcapPaths
            )

        except Exception as e:
            self.logger.error(f"MainTest failed: {str(e)}")
            # 更新任务状态为失败
            try:
                task = self.loadTask(taskId)
                if task:
                    self.updateTask(taskId,TASK_FINISH,TASK_FAIL,'')
            except Exception as save_error:
                self.logger.error(f"Failed to update task status: {str(save_error)}")
            raise

    # afterTest 方法已移至 pcapTest 类中处理

    # report 方法已移至 pcapTest 类中处理

# dut 类已移至 play.py 中实现
