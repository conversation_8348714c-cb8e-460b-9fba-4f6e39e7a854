<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页签切换测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 横向菜单栏样式 */
        .horizontal-menu {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .menu-nav {
            display: flex;
            justify-content: space-around;
            align-items: center;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .menu-item {
            flex: 1;
            text-align: center;
        }

        .menu-link {
            display: block;
            padding: 15px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .menu-link:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.1);
            text-decoration: none;
        }

        .menu-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .content-panel {
            display: none;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 25px rgba(0, 0, 0, 0.08);
            padding: 30px;
            min-height: 400px;
        }

        .content-panel.active {
            display: block;
        }

        .test-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }

        .test-status.error {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>页签切换功能测试</h1>
        
        <div class="test-status" id="testStatus">测试准备中...</div>

        <!-- 横向菜单栏 -->
        <div class="horizontal-menu">
            <ul class="menu-nav">
                <li class="menu-item">
                    <a href="#" class="menu-link active" data-panel="config">
                        <i class="fas fa-cog"></i> 配置设置
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" data-panel="task">
                        <i class="fas fa-plus-circle"></i> 新建任务
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" data-panel="result">
                        <i class="fas fa-list-alt"></i> 任务结果
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" data-panel="log">
                        <i class="fas fa-file-alt"></i> 运行日志
                    </a>
                </li>
            </ul>
        </div>

        <!-- 内容面板 -->
        <div class="content-container">
            <div class="content-panel active" id="config-panel">
                <h3><i class="fas fa-cog text-primary"></i> 配置设置面板</h3>
                <p>这是配置设置的内容区域。</p>
                <div class="alert alert-info">
                    <strong>测试说明：</strong> 点击上方的页签按钮，观察面板是否正确切换。
                </div>
            </div>

            <div class="content-panel" id="task-panel">
                <h3><i class="fas fa-plus-circle text-success"></i> 新建任务面板</h3>
                <p>这是新建任务的内容区域。</p>
                <div class="alert alert-success">
                    <strong>成功！</strong> 如果您能看到这个面板，说明页签切换功能正常工作。
                </div>
            </div>

            <div class="content-panel" id="result-panel">
                <h3><i class="fas fa-list-alt text-warning"></i> 任务结果面板</h3>
                <p>这是任务结果的内容区域。</p>
                <div class="alert alert-warning">
                    <strong>注意：</strong> 页签切换应该有平滑的动画效果。
                </div>
            </div>

            <div class="content-panel" id="log-panel">
                <h3><i class="fas fa-file-alt text-danger"></i> 运行日志面板</h3>
                <p>这是运行日志的内容区域。</p>
                <div class="alert alert-danger">
                    <strong>重要：</strong> 每次只应该有一个面板可见。
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h4>测试结果</h4>
            <div id="testResults" class="alert alert-secondary">
                <p>点击页签进行测试...</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            let testCount = 0;
            let successCount = 0;

            function updateTestStatus(message, isError = false) {
                const status = $('#testStatus');
                status.text(message);
                status.removeClass('error');
                if (isError) {
                    status.addClass('error');
                }
            }

            function logTestResult(message) {
                const results = $('#testResults');
                const timestamp = new Date().toLocaleTimeString();
                results.append(`<p>[${timestamp}] ${message}</p>`);
            }

            // 页签切换事件 - 使用与实际代码相同的逻辑
            $(document).off('click', '.menu-link').on('click', '.menu-link', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const $this = $(this);
                const targetPanel = $this.data('panel');
                testCount++;

                console.log('测试点击:', targetPanel, '测试次数:', testCount);
                logTestResult(`点击页签: ${targetPanel}`);

                if (!targetPanel) {
                    updateTestStatus('错误：未找到面板数据', true);
                    logTestResult('❌ 错误：未找到面板数据');
                    return;
                }

                try {
                    // 更新菜单状态
                    $('.menu-link').removeClass('active');
                    $this.addClass('active');

                    // 切换内容面板
                    $('.content-panel').removeClass('active').fadeOut(200, function() {
                        const targetPanelElement = $(`#${targetPanel}-panel`);

                        if (targetPanelElement.length === 0) {
                            updateTestStatus('错误：未找到目标面板', true);
                            logTestResult(`❌ 错误：未找到面板 #${targetPanel}-panel`);
                            return;
                        }

                        targetPanelElement.addClass('active').fadeIn(300);
                        successCount++;

                        logTestResult(`✅ 成功切换到: ${targetPanel}`);
                        updateTestStatus(`测试成功 ${successCount}/${testCount}`);

                        // 验证状态
                        setTimeout(function() {
                            const visiblePanels = $('.content-panel:visible').length;
                            const activePanels = $('.content-panel.active').length;
                            
                            if (visiblePanels === 1 && activePanels === 1) {
                                logTestResult(`✅ 状态验证通过：可见面板=${visiblePanels}，激活面板=${activePanels}`);
                            } else {
                                logTestResult(`❌ 状态验证失败：可见面板=${visiblePanels}，激活面板=${activePanels}`);
                                updateTestStatus('状态验证失败', true);
                            }
                        }, 350);
                    });

                } catch (error) {
                    updateTestStatus('切换失败', true);
                    logTestResult(`❌ 切换失败: ${error.message}`);
                    console.error('切换面板时出错:', error);
                }
            });

            // 初始化测试
            updateTestStatus('测试就绪');
            logTestResult('页签切换测试初始化完成');
            
            // 自动测试所有页签
            setTimeout(function() {
                logTestResult('开始自动测试...');
                const panels = ['task', 'result', 'log', 'config'];
                let index = 0;
                
                function testNext() {
                    if (index < panels.length) {
                        $(`.menu-link[data-panel="${panels[index]}"]`).click();
                        index++;
                        setTimeout(testNext, 1000);
                    } else {
                        logTestResult(`自动测试完成！成功率: ${successCount}/${testCount}`);
                    }
                }
                
                testNext();
            }, 2000);
        });
    </script>
</body>
</html>
