/* HTTP负载模拟访问插件样式 */

/* 基础容器样式 */
.http-load-simulator-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
}

/* 移除导航栏样式，使用base.html的导航 */

/* 确保页面头部宽度一致 */
.page-header {
    width: 100%;
    max-width: 100%;
}

.header-content {
    width: 100%;
    max-width: 100%;
}

/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-title i {
    font-size: 2.5rem;
    color: white;
}

.header-title h1 {
    margin: 0;
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.action-btn.active {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
}

/* 内容面板 */
.content-panels {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 面板样式 */
.panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    overflow: hidden;
}

.panel-header {
    background: #f8fafc;
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-header .panel-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 1.1rem;
}

.panel-title {
    flex: 1;
}

.panel-title h3 {
    margin: 0 0 5px 0;
    color: #2d3748;
    font-size: 1.3rem;
    font-weight: 600;
}

.panel-title p {
    margin: 0;
    color: #718096;
    font-size: 0.9rem;
}

.panel-actions {
    display: flex;
    gap: 10px;
}

.panel-body {
    padding: 30px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.form-label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #4a5568;
    font-weight: 600;
    font-size: 0.95rem;
}

.form-label i {
    margin-right: 8px;
    color: #667eea;
    width: 16px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Courier New', monospace;
    resize: vertical;
    min-height: 200px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-hint {
    margin-top: 5px;
    color: #718096;
    font-size: 0.85rem;
    line-height: 1.4;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.btn i {
    margin-right: 8px;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
    transform: translateY(-1px);
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
    transform: translateY(-1px);
}

.btn-info {
    background: #4299e1;
    color: white;
}

.btn-info:hover {
    background: #3182ce;
    transform: translateY(-1px);
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover {
    background: #e53e3e;
    transform: translateY(-1px);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* 状态面板样式 */
.status-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.status-item {
    display: flex;
    flex-direction: column;
}

.status-label {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.status-value {
    color: #2d3748;
    font-weight: 600;
    font-size: 1rem;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: #fed7d7;
    color: #c53030;
}

.status-running {
    background: #bee3f8;
    color: #2b6cb0;
}

.status-completed {
    background: #c6f6d5;
    color: #2f855a;
}

.status-failed {
    background: #fed7d7;
    color: #c53030;
}

/* 进度条样式 */
.progress-section {
    margin-bottom: 25px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.5s ease;
    width: 0%;
}

.progress-text {
    color: #4a5568;
    font-size: 0.9rem;
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* 任务列表样式 */
.loading-state,
.empty-state {
    text-align: center;
    padding: 60px 20px;
    width: 100%;
    max-width: 100%;
    min-width: 850px; /* 与任务列表保持相同的最小宽度 */
    box-sizing: border-box;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.loading-icon,
.empty-icon {
    font-size: 3rem;
    color: #cbd5e0;
    margin-bottom: 20px;
}

.loading-text h4,
.empty-text h4 {
    margin: 0 0 10px 0;
    color: #4a5568;
    font-size: 1.2rem;
}

.empty-text p {
    margin: 0 0 25px 0;
    color: #718096;
}

.task-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
}

.task-header {
    display: grid;
    grid-template-columns: 80px 280px 1fr 100px 150px 1fr 120px;
    gap: 20px;
    padding: 15px 20px;
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.9rem;
    min-width: 0; /* 防止网格项溢出 */
    width: 100%;
    max-width: 100%;
}

.task-body {
    max-height: 600px;
    overflow-y: auto;
    width: 100%;
    max-width: 100%;
}

.task-row {
    display: grid;
    grid-template-columns: 80px 280px 1fr 100px 150px 1fr 120px;
    gap: 20px;
    padding: 15px 20px;
    border-bottom: 1px solid #e2e8f0;
    align-items: center;
    transition: background 0.2s ease;
    min-width: 0; /* 防止网格项溢出 */
    width: 100%;
    max-width: 100%;
}

.task-row:hover {
    background: #f8fafc;
}

.task-col {
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.task-select {
    display: flex;
    align-items: center;
    gap: 8px;
}

.task-select input[type="checkbox"] {
    margin: 0;
}

.task-select label {
    font-size: 0.85rem;
    color: #718096;
    cursor: pointer;
    margin: 0;
}

.task-id {
    font-family: monospace;
    font-size: 0.85rem;
    color: #4a5568;
}

.task-name {
    font-weight: 500;
    color: #2d3748;
}

.task-time {
    font-size: 0.9rem;
    color: #718096;
}

.task-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.task-actions .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* 批量操作样式 */
.batch-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    margin-bottom: 20px;
}

.batch-info {
    color: #c53030;
    font-weight: 500;
}

.batch-buttons {
    display: flex;
    gap: 10px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90%;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-small .modal-content {
    max-width: 500px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.modal-header h3 {
    margin: 0;
    color: #2d3748;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #718096;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e2e8f0;
    color: #4a5568;
}

.modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
    background: #f8fafc;
}

/* 详情样式 */
.detail-section {
    margin-bottom: 25px;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    color: #2d3748;
    font-size: 1.1rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 8px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-item label {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 5px;
    font-weight: 500;
}

.detail-item span {
    color: #2d3748;
    font-weight: 600;
}

.code-block {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
}

/* 确认对话框样式 */
.confirm-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.confirm-icon {
    font-size: 2.5rem;
    color: #f56565;
}

.confirm-text p {
    margin: 0 0 8px 0;
    color: #2d3748;
}

.confirm-text .text-muted {
    color: #718096;
    font-size: 0.9rem;
}

/* 提示框样式 */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    max-width: 400px;
}

.alert {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 10px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.alert.show {
    transform: translateX(0);
}

.alert-success {
    border-left: 4px solid #48bb78;
}

.alert-error {
    border-left: 4px solid #f56565;
}

.alert-info {
    border-left: 4px solid #4299e1;
}

.alert-icon {
    font-size: 1.2rem;
}

.alert-success .alert-icon {
    color: #48bb78;
}

.alert-error .alert-icon {
    color: #f56565;
}

.alert-info .alert-icon {
    color: #4299e1;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 4px 0;
    font-size: 0.95rem;
}

.alert-message {
    color: #718096;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .http-load-simulator-container {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-title h1 {
        font-size: 1.5rem;
    }

    .header-actions {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .action-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .panel-body {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .task-header,
    .task-row {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: left;
    }

    .task-col {
        white-space: normal;
        word-wrap: break-word;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-body {
        padding: 20px;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }
}
