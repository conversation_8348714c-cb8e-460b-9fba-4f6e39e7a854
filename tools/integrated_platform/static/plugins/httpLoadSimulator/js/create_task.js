/**
 * HTTP负载模拟访问插件 - 创建任务页面脚本
 */

let currentTaskId = null;
let statusCheckInterval = null;
let csvData = null;
let currentTaskMode = 'single';

document.addEventListener('DOMContentLoaded', function() {
    console.log('HTTP负载模拟创建任务页面已加载');
    
    // 初始化页面
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 设置默认HTTP负载示例
    setDefaultHttpPayload();

    // 绑定事件监听器
    bindEventListeners();

    // 设置拖拽上传功能
    setupDragAndDrop();
}

/**
 * 设置默认HTTP负载示例
 */
function setDefaultHttpPayload() {
    const httpPayloadTextarea = document.getElementById('httpPayload');
    if (httpPayloadTextarea && !httpPayloadTextarea.value.trim()) {
        const defaultPayload = `POST /api/test HTTP/1.1
Host: example.com
Content-Type: application/json
Content-Length: 25
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
Accept: application/json, text/plain, */*
Connection: close

{"key": "value"}`;
        
        httpPayloadTextarea.value = defaultPayload;
    }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 绑定表单验证
    const inputs = document.querySelectorAll('.form-input, .form-textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

/**
 * 处理表单提交
 */
async function handleFormSubmit(event) {
    event.preventDefault();

    // 验证表单
    if (!validateForm()) {
        return;
    }

    if (currentTaskMode === 'single') {
        // 单个测试模式
        const formData = {
            taskName: document.getElementById('taskName').value.trim(),
            httpPayload: document.getElementById('httpPayload').value.trim(),
            targetIp: document.getElementById('targetIp').value.trim(),
            targetPort: parseInt(document.getElementById('targetPort').value),
            taskMode: 'single'
        };

        await submitTask(formData);
    } else if (currentTaskMode === 'batch') {
        // 批量测试模式
        const formData = {
            taskName: document.getElementById('taskName').value.trim(),
            taskMode: 'batch',
            csvData: csvData
        };

        await submitBatchTask(formData);
    }
}

/**
 * 验证表单
 */
function validateForm() {
    let isFormValid = true;

    // 验证任务名称（必填）
    const taskNameInput = document.getElementById('taskName');
    if (!validateField(taskNameInput)) {
        isFormValid = false;
    }

    if (currentTaskMode === 'single') {
        // 单个测试模式验证
        const requiredInputs = ['httpPayload', 'targetIp', 'targetPort'];
        requiredInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input && !validateField(input)) {
                isFormValid = false;
            }
        });
    } else if (currentTaskMode === 'batch') {
        // 批量测试模式验证
        if (!csvData || csvData.length === 0) {
            showAlert('请上传包含测试项目的CSV文件', 'error');
            isFormValid = false;
        }
    }

    return isFormValid;
}

/**
 * 验证单个字段
 */
function validateField(input) {
    const value = input.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // 检查必填字段
    if (input.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = '此字段为必填项';
    }
    
    // 特定字段验证
    switch (input.id) {
        case 'targetIp':
            if (value && !isValidIpAddress(value)) {
                isValid = false;
                errorMessage = '请输入有效的IP地址';
            }
            break;
        case 'targetPort':
            const port = parseInt(value);
            if (value && (isNaN(port) || port < 1 || port > 65535)) {
                isValid = false;
                errorMessage = '端口号必须在1-65535之间';
            }
            break;
        case 'httpPayload':
            if (value && !isValidHttpPayload(value)) {
                isValid = false;
                errorMessage = 'HTTP负载格式不正确，请检查请求行和请求头格式';
            }
            break;
    }
    
    // 显示或清除错误
    if (isValid) {
        clearFieldError(input);
    } else {
        showFieldError(input, errorMessage);
    }
    
    return isValid;
}

/**
 * 验证IP地址格式
 */
function isValidIpAddress(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
}

/**
 * 验证HTTP负载格式
 */
function isValidHttpPayload(payload) {
    const lines = payload.trim().split('\n');
    if (lines.length === 0) return false;
    
    // 检查请求行格式
    const requestLine = lines[0].trim();
    const requestParts = requestLine.split(' ');
    if (requestParts.length < 3) return false;
    
    // 检查HTTP方法
    const method = requestParts[0].toUpperCase();
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS', 'PATCH'];
    if (!validMethods.includes(method)) return false;
    
    // 检查HTTP版本
    const version = requestParts[2];
    if (!version.startsWith('HTTP/')) return false;
    
    return true;
}

/**
 * 显示字段错误
 */
function showFieldError(input, message) {
    clearFieldError(input);
    
    input.classList.add('error');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    
    input.parentNode.appendChild(errorDiv);
}

/**
 * 清除字段错误
 */
function clearFieldError(input) {
    input.classList.remove('error');
    
    const errorDiv = input.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * 提交任务
 */
async function submitTask(formData) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    try {
        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>创建中...</span>';
        
        const response = await fetch('/api/plugins/httpLoadSimulator/tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentTaskId = result.data.taskId;
            showTaskStatus();
            startStatusCheck();
            showAlert('HTTP负载模拟任务创建成功，正在执行中...', 'success');
        } else {
            throw new Error(result.message || '创建任务失败');
        }
        
    } catch (error) {
        console.error('提交任务失败:', error);
        showAlert(`创建任务失败: ${error.message}`, 'error');
    } finally {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * 提交批量任务
 */
async function submitBatchTask(formData) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;

    try {
        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>创建批量任务中...</span>';

        const response = await fetch('/api/plugins/httpLoadSimulator/batch-tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            currentTaskId = result.data.taskId;
            showTaskStatus();
            startStatusCheck();
            showAlert(`批量HTTP负载模拟任务创建成功，共${csvData.length}个测试项目，正在执行中...`, 'success');
        } else {
            throw new Error(result.message || '创建批量任务失败');
        }

    } catch (error) {
        console.error('提交批量任务失败:', error);
        showAlert(`创建批量任务失败: ${error.message}`, 'error');
    } finally {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * 显示任务状态面板
 */
function showTaskStatus() {
    const statusPanel = document.getElementById('statusPanel');
    const taskIdElement = document.getElementById('taskId');
    
    if (statusPanel && taskIdElement) {
        taskIdElement.textContent = currentTaskId;
        statusPanel.style.display = 'block';
        
        // 滚动到状态面板
        statusPanel.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * 开始状态检查
 */
function startStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
    
    statusCheckInterval = setInterval(checkTaskStatus, 2000);
}

/**
 * 检查任务状态
 */
async function checkTaskStatus() {
    if (!currentTaskId) return;
    
    try {
        const response = await fetch(`/api/plugins/httpLoadSimulator/tasks/${currentTaskId}/status`);
        const result = await response.json();
        
        if (result.success) {
            updateTaskStatus(result.data);
            
            // 如果任务完成，停止检查
            if (result.data.status === 'completed' || result.data.status === 'failed') {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }
    } catch (error) {
        console.error('检查任务状态失败:', error);
    }
}

/**
 * 更新任务状态显示
 */
function updateTaskStatus(taskData) {
    const statusElement = document.getElementById('taskStatus');
    const resultElement = document.getElementById('taskResult');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const actionButtons = document.getElementById('actionButtons');
    
    // 更新状态显示
    if (statusElement) {
        const statusClass = `status-${taskData.status}`;
        const statusText = getStatusText(taskData.status);
        statusElement.innerHTML = `<span class="status-badge ${statusClass}">${statusText}</span>`;
    }
    
    // 更新结果显示
    if (resultElement) {
        resultElement.textContent = taskData.result || '-';
    }
    
    // 更新进度条
    if (progressFill && progressText) {
        let progress = 0;
        let progressMessage = '';
        
        switch (taskData.status) {
            case 'pending':
                progress = 10;
                progressMessage = '任务排队中...';
                break;
            case 'running':
                progress = 50;
                progressMessage = '正在执行HTTP负载模拟...';
                break;
            case 'completed':
                progress = 100;
                progressMessage = '任务执行完成';
                break;
            case 'failed':
                progress = 100;
                progressMessage = '任务执行失败';
                break;
        }
        
        progressFill.style.width = `${progress}%`;
        progressText.textContent = progressMessage;
    }
    
    // 显示操作按钮
    if (actionButtons && (taskData.status === 'completed' || taskData.status === 'failed')) {
        actionButtons.style.display = 'flex';

        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.style.display = taskData.hasReport ? 'inline-flex' : 'none';
        }

        // 任务完成后3秒自动跳转到任务结果页面
        if (taskData.status === 'completed') {
            setTimeout(() => {
                showAlert('任务执行完成，即将跳转到任务结果页面...', 'success');
                setTimeout(() => {
                    window.location.href = '/plugins/httpLoadSimulator/task-results';
                }, 1500);
            }, 3000);
        }
    }
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'running': '执行中',
        'completed': '已完成',
        'failed': '失败'
    };

    return statusMap[status] || status;
}

/**
 * 下载结果文件
 */
function downloadResult() {
    if (currentTaskId) {
        window.open(`/api/plugins/httpLoadSimulator/tasks/${currentTaskId}/download-report`, '_blank');
    }
}

/**
 * 查看结果
 */
function viewResults() {
    window.location.href = '/plugins/httpLoadSimulator/task-results';
}

/**
 * 重置表单
 */
function resetForm() {
    const form = document.getElementById('createTaskForm');
    if (form) {
        form.reset();

        // 清除所有错误状态
        const inputs = form.querySelectorAll('.form-input, .form-textarea');
        inputs.forEach(input => {
            clearFieldError(input);
        });

        // 重新设置默认HTTP负载
        setDefaultHttpPayload();

        // 隐藏状态面板
        const statusPanel = document.getElementById('statusPanel');
        if (statusPanel) {
            statusPanel.style.display = 'none';
        }

        // 清除状态检查
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }

        currentTaskId = null;
    }
}

/**
 * 显示提示框
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) return;

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;

    const iconMap = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle'
    };

    alertDiv.innerHTML = `
        <div class="alert-icon">
            <i class="${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="alert-content">
            <div class="alert-message">${message}</div>
        </div>
    `;

    alertContainer.appendChild(alertDiv);

    // 显示动画
    setTimeout(() => {
        alertDiv.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    }, 4000);
}

// 添加CSS样式用于字段错误显示
const style = document.createElement('style');
style.textContent = `
    .form-input.error,
    .form-textarea.error {
        border-color: #f56565 !important;
        box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1) !important;
    }

    .field-error {
        color: #f56565;
        font-size: 0.85rem;
        margin-top: 5px;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .field-error::before {
        content: "⚠";
        font-size: 0.9rem;
    }
`;
document.head.appendChild(style);

/**
 * 切换任务模式
 */
function toggleTaskMode() {
    const singleMode = document.querySelector('input[name="taskMode"][value="single"]');
    const batchMode = document.querySelector('input[name="taskMode"][value="batch"]');
    const singleConfig = document.getElementById('singleTestConfig');
    const batchConfig = document.getElementById('batchTestConfig');

    if (singleMode.checked) {
        currentTaskMode = 'single';
        singleConfig.style.display = 'block';
        batchConfig.style.display = 'none';

        // 重置批量配置
        resetBatchConfig();

        // 设置单个模式的必填字段
        document.getElementById('httpPayload').required = true;
        document.getElementById('targetIp').required = true;
        document.getElementById('targetPort').required = true;
    } else if (batchMode.checked) {
        currentTaskMode = 'batch';
        singleConfig.style.display = 'none';
        batchConfig.style.display = 'block';

        // 取消单个模式的必填字段
        document.getElementById('httpPayload').required = false;
        document.getElementById('targetIp').required = false;
        document.getElementById('targetPort').required = false;
    }
}

/**
 * 重置批量配置
 */
function resetBatchConfig() {
    csvData = null;
    document.getElementById('csvFile').value = '';
    document.getElementById('csvPreviewGroup').style.display = 'none';

    // 重置上传区域样式
    const uploadArea = document.getElementById('csvUploadArea');
    uploadArea.classList.remove('dragover');
}

/**
 * 处理CSV文件选择
 */
function handleCsvFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processCsvFile(file);
    }
}

/**
 * 处理CSV文件
 */
function processCsvFile(file) {
    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.csv')) {
        showAlert('请选择CSV格式的文件', 'error');
        return;
    }

    // 验证文件大小（最大10MB）
    if (file.size > 10 * 1024 * 1024) {
        showAlert('文件大小不能超过10MB', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const csvContent = e.target.result;
            const parsedData = parseCsvContent(csvContent);

            if (parsedData && parsedData.length > 0) {
                csvData = parsedData;
                displayCsvPreview(file, parsedData);
                showAlert(`成功解析CSV文件，共${parsedData.length}个测试项目`, 'success');
            } else {
                showAlert('CSV文件格式错误或为空', 'error');
            }
        } catch (error) {
            console.error('解析CSV文件失败:', error);
            showAlert('解析CSV文件失败，请检查文件格式', 'error');
        }
    };

    reader.onerror = function() {
        showAlert('读取文件失败', 'error');
    };

    reader.readAsText(file, 'UTF-8');
}

/**
 * 解析CSV内容
 */
function parseCsvContent(csvContent) {
    const lines = csvContent.split('\n').filter(line => line.trim());
    const data = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        // 简单的CSV解析（支持逗号分隔）
        const columns = parseCsvLine(line);

        if (columns.length >= 2) {
            const testName = columns[0].trim();
            const httpPayload = columns[1].trim();

            if (testName && httpPayload) {
                data.push({
                    testName: testName,
                    httpPayload: httpPayload,
                    rowIndex: i + 1
                });
            }
        }
    }

    return data;
}

/**
 * 解析CSV行（处理引号包围的字段）
 */
function parseCsvLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current);
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current);
    return result;
}

/**
 * 显示CSV预览
 */
function displayCsvPreview(file, data) {
    // 显示文件信息
    document.getElementById('csvFileName').textContent = file.name;
    document.getElementById('csvFileSize').textContent = formatFileSize(file.size);
    document.getElementById('csvRowCount').textContent = `${data.length} 个测试项目`;

    // 生成预览表格
    const previewTable = document.getElementById('csvPreviewTable');
    let tableHtml = `
        <table>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>测试项目</th>
                    <th>HTTP负载</th>
                </tr>
            </thead>
            <tbody>
    `;

    // 只显示前10行作为预览
    const previewData = data.slice(0, 10);
    previewData.forEach((item, index) => {
        tableHtml += `
            <tr>
                <td>${index + 1}</td>
                <td title="${escapeHtml(item.testName)}">${escapeHtml(item.testName)}</td>
                <td title="${escapeHtml(item.httpPayload)}">${escapeHtml(item.httpPayload.substring(0, 100))}${item.httpPayload.length > 100 ? '...' : ''}</td>
            </tr>
        `;
    });

    if (data.length > 10) {
        tableHtml += `
            <tr>
                <td colspan="3" style="text-align: center; color: #6c757d; font-style: italic;">
                    ... 还有 ${data.length - 10} 个测试项目
                </td>
            </tr>
        `;
    }

    tableHtml += `
            </tbody>
        </table>
    `;

    previewTable.innerHTML = tableHtml;

    // 显示预览区域
    document.getElementById('csvPreviewGroup').style.display = 'block';
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 下载CSV模板文件
 */
function downloadCsvTemplate() {
    const csvContent = `测试项目,HTTP负载
登录接口测试,"POST /api/login HTTP/1.1
Host: example.com
Content-Type: application/json
Content-Length: 45

{""username"": ""test"", ""password"": ""123456""}"
用户信息查询,"GET /api/user/profile HTTP/1.1
Host: example.com
Authorization: Bearer token123
Accept: application/json"
数据提交测试,"POST /api/data HTTP/1.1
Host: example.com
Content-Type: application/x-www-form-urlencoded
Content-Length: 25

name=test&value=example"`;

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', 'http_load_template.csv');
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showAlert('CSV模板文件下载已开始', 'success');
}

/**
 * 设置拖拽上传功能
 */
function setupDragAndDrop() {
    const uploadArea = document.getElementById('csvUploadArea');

    if (!uploadArea) return;

    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            document.getElementById('csvFile').files = files;
            processCsvFile(file);
        }
    });
}
