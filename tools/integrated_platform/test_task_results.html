<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务结果测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .loading-state, .empty-state, .task-list {
            padding: 20px;
            text-align: center;
        }
        .task-list {
            text-align: left;
        }
        .task-header, .task-row {
            display: flex;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .task-header {
            background: #f8f9fa;
            font-weight: bold;
        }
        .task-col {
            flex: 1;
            padding: 5px;
        }
        .btn {
            padding: 5px 10px;
            margin: 2px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .status-completed { color: #28a745; }
        .status-running { color: #ffc107; }
        .status-failed { color: #dc3545; }
        .status-pending { color: #6c757d; }
        #console {
            background: #000;
            color: #0f0;
            padding: 10px;
            margin-top: 20px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HTTP负载模拟访问 - 任务结果测试</h1>
        
        <!-- 加载状态 -->
        <div class="loading-state" id="loadingState">
            <h4>正在加载任务列表...</h4>
            <p>请稍候</p>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <h4>暂无任务记录</h4>
            <p>还没有执行过HTTP负载模拟任务</p>
        </div>

        <!-- 任务列表 -->
        <div class="task-list" id="taskList" style="display: none;">
            <div class="task-header">
                <div class="task-col">任务ID</div>
                <div class="task-col">任务名称</div>
                <div class="task-col">状态</div>
                <div class="task-col">创建时间</div>
                <div class="task-col">结果</div>
                <div class="task-col">操作</div>
            </div>
            <div class="task-body" id="taskBody">
                <!-- 任务行将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <div>
            <button onclick="testLoadTasks()" class="btn btn-info">测试加载任务</button>
            <button onclick="clearConsole()" class="btn btn-danger">清空控制台</button>
        </div>
        
        <div id="console"></div>
    </div>

    <script>
        // 重定向console.log到页面
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleDiv.innerHTML += '<div style="color: #0f0;">[LOG] ' + args.join(' ') + '</div>';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            consoleDiv.innerHTML += '<div style="color: #f00;">[ERROR] ' + args.join(' ') + '</div>';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        function clearConsole() {
            consoleDiv.innerHTML = '';
        }
        
        // 测试函数
        function testLoadTasks() {
            console.log('开始测试加载任务...');
            
            // 模拟API调用
            fetch('/api/plugins/httpLoadSimulator/tasks')
                .then(response => {
                    console.log('API响应状态:', response.status);
                    return response.json();
                })
                .then(result => {
                    console.log('API响应数据:', JSON.stringify(result, null, 2));
                    
                    if (result.success) {
                        const tasks = result.data || [];
                        console.log('任务数量:', tasks.length);
                        
                        if (tasks.length === 0) {
                            showEmptyState();
                        } else {
                            showTaskList(tasks);
                        }
                    } else {
                        console.error('API返回错误:', result.message);
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                });
        }
        
        function showEmptyState() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('emptyState').style.display = 'block';
            document.getElementById('taskList').style.display = 'none';
        }
        
        function showTaskList(tasks) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';
            document.getElementById('taskList').style.display = 'block';
            
            const taskBody = document.getElementById('taskBody');
            taskBody.innerHTML = '';
            
            tasks.forEach(task => {
                const row = document.createElement('div');
                row.className = 'task-row';
                row.innerHTML = `
                    <div class="task-col">${task.id}</div>
                    <div class="task-col">${task.name}</div>
                    <div class="task-col status-${task.status}">${getStatusText(task.status)}</div>
                    <div class="task-col">${task.createTime}</div>
                    <div class="task-col">${task.result || '-'}</div>
                    <div class="task-col">
                        <button class="btn btn-info">查看</button>
                        <button class="btn btn-danger">删除</button>
                    </div>
                `;
                taskBody.appendChild(row);
            });
            
            console.log('任务列表渲染完成，共', tasks.length, '个任务');
        }
        
        function getStatusText(status) {
            const statusMap = {
                'pending': '等待中',
                'running': '执行中',
                'completed': '已完成',
                'failed': '失败'
            };
            return statusMap[status] || status;
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成');
            setTimeout(testLoadTasks, 1000);
        });
    </script>
</body>
</html>
