#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新架构启动脚本
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
PATH=os.path.dirname(os.path.abspath(__file__))

from core.app import create_app, shutdown_app


def main():
    """主函数"""
    # 获取运行参数
    config_name = os.environ.get('FLASK_ENV') or 'development'
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 8083))
    debug = os.environ.get('DEBUG', 'True').lower() == 'true'
    
    print("=" * 60)
    print("集成工具平台 v2.0 - 插件式架构")
    print("=" * 60)
    print(f"配置环境: {config_name}")
    print(f"访问地址: http://{host}:{port}")
    print(f"调试模式: {'开启' if debug else '关闭'}")
    print("=" * 60)
    
    try:
        # 创建应用
        app = create_app(config_name)
        
        # 启动应用
        app.run(host=host, port=port, debug=debug)
        
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        if 'app' in locals():
            shutdown_app(app)
    except Exception as e:
        print(f"启动失败: {e}")
        if 'app' in locals():
            shutdown_app(app)
        sys.exit(1)


if __name__ == '__main__':
    main()
